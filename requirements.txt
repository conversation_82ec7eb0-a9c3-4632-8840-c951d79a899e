aiohttp>=3.8.0
google-generativeai>=0.3.0
PyMuPDF>=1.20.0  # PDF処理用の代替ライブラリ（pdf2imageのフォールバックとして使用）
Pillow>=9.0.0    # 画像処理ライブラリ
python-dotenv>=0.19.0
pydantic>=2.0.0
typer>=0.9.0
rich>=13.0.0
asyncio>=3.4.3
attrs>=23.0.0

# PDF変換用ライブラリ - システム依存ライブラリも必要
# macOS: brew install poppler
# Linux: sudo apt-get install poppler-utils
# Windows: https://github.com/oschwartz10612/poppler-windows/releases からダウンロード
pdf2image>=1.16.0

numpy
