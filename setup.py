from setuptools import setup, find_packages

setup(
    name="mistral_exam_marker",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "aiohttp>=3.8.0",
        "asyncio>=3.4.3",
        "attrs>=23.0.0",
        "google-generativeai>=0.3.0",
        "pillow>=9.0.0",
        "pydantic>=2.0.0",
        "pymupdf>=1.20.0",
        "python-dotenv>=0.19.0",
        "rich>=13.0.0",
        "typer>=0.9.0",
    ],
    entry_points={
        "console_scripts": [
            "mistral-exam-marker = mistral_exam_marker.__main__:main",
        ],
    },
)
