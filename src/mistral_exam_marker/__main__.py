"""
Mistral Exam Markerのメインエントリーポイント
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Optional, List
import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from dotenv import load_dotenv
# カレントディレクトリの .env ファイルを優先的に読み込むための設定
PROJECT_ROOT = Path(__file__).resolve().parents[2]
CURRENT_DIR = Path.cwd()
import tkinter as tk
from tkinter import filedialog, messagebox
import threading

from .marker import MistralExamMarker

# ロガーの設定
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)

logger = logging.getLogger("rich")
console = Console()
app = typer.Typer()

def check_api_keys() -> bool:
    """必要なAPIキーが設定されているか確認"""
    required_keys = ["MISTRAL_API_KEY", "GEMINI_API_KEY"]
    missing_keys = [key for key in required_keys if not os.getenv(key)]

    if missing_keys:
        messagebox.showerror(
            "API Key Error",
            f"The following API keys are not set: {', '.join(missing_keys)}\n\nPlease check your .env file."
        )
        return False
    return True

async def process_single_pdf(marker: MistralExamMarker, pdf_path: str) -> None:
    """単一のPDFを処理"""
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            progress.add_task(f"Processing {Path(pdf_path).name}...", total=None)
            result = await marker.process_pdf(pdf_path)

            if isinstance(result, str):
                console.print(result)
            else:
                console.print("[red]Error: Unexpected return type.")

    except Exception as e:
        console.print(f"[red]Error processing {pdf_path}: {str(e)}")

async def process_folder(marker: MistralExamMarker, folder_path: str, output_to_same_folder: bool = True) -> None:
    """フォルダ内のPDFファイルを処理"""
    folder = Path(folder_path)
    pdf_files = list(folder.glob("*.pdf"))

    if not pdf_files:
        messagebox.showinfo("Information", f"No PDF files found in folder: {folder_path}")
        return

    console.print(f"[green]Number of PDF files to process: {len(pdf_files)}")

    # 出力先を設定
    if output_to_same_folder:
        marker.set_output_directory(str(folder))

    # すべてのPDFを処理
    for pdf_path in pdf_files:
        console.print(f"[blue]Processing: {pdf_path.name}")
        await process_single_pdf(marker, str(pdf_path))

    # 統計情報
    stats = marker.get_statistics()
    console.print("\n[green]Processing completed!\n")
    console.print("[bold]Statistics:")
    console.print(f"Processed files: {stats['total_exams']}")
    console.print(f"Total pages: {stats['total_pages']}")
    console.print(f"Total processing time: {stats['processing_time']:.1f} seconds")
    console.print(f"Average processing time: {stats['average_time_per_exam']:.1f} seconds/file")

    # 処理完了のメッセージを表示
    messagebox.showinfo(
            "Processing Complete",
            f"All PDF files have been processed!\n\n"
            f"Processed files: {stats['total_exams']}\n"
            f"Total processing time: {stats['processing_time']:.1f} seconds\n\n"
            f"Output folder: {marker.get_output_directory()}"
        )

def run_gui():
    """GUIモードで実行"""
    # .envファイルの読み込み（カレントディレクトリを優先）
    current_dotenv_path = CURRENT_DIR / ".env"
    project_dotenv_path = PROJECT_ROOT / ".env"

    if current_dotenv_path.exists():
        logger.info(f"Loading .env from current directory: {current_dotenv_path}")
        load_dotenv(dotenv_path=str(current_dotenv_path))
    elif project_dotenv_path.exists():
        logger.info(f"Loading .env from project root: {project_dotenv_path}")
        load_dotenv(dotenv_path=str(project_dotenv_path))
    else:
        logger.info("No .env file found in current or project directory. Using default load_dotenv()")
        load_dotenv()

    # APIキーのチェック
    if not check_api_keys():
        sys.exit(1)

    # ルートウィンドウを作成して非表示
    root = tk.Tk()
    root.withdraw()

    # フォルダ選択ダイアログを表示
    folder_path = filedialog.askdirectory(
        title="Select folder containing PDF files to grade",
        mustexist=True
    )

    if not folder_path:
        console.print("[yellow]No folder selected. Exiting...")
        return

    # 採点者名の設定
    marker_name = os.getenv("MARKER_NAME", "Unknown Marker")

    # MistralExamMarkerの初期化
    marker = MistralExamMarker(
        mistral_api_key=os.getenv("MISTRAL_API_KEY", ""),
        gemini_api_key=os.getenv("GEMINI_API_KEY", ""),
        marker_name=marker_name
    )

    console.print(f"[green]Starting grading...\n")
    console.print(f"Marker: {marker_name}")
    console.print(f"Selected folder: {folder_path}\n")

    # 非同期処理を実行
    asyncio.run(process_folder(marker, folder_path, output_to_same_folder=True))
    marker.save_all_feedback() # 全フィードバックを保存

    # tkinterのイベントループを終了
    root.destroy()

@app.command()
def mark(
    pdf_paths: List[str] = typer.Argument(
        None,
        help="Path to PDF files to process (GUI mode if not specified)",
        exists=True,
        dir_okay=False,
        resolve_path=True,
    ),
    output_dir: str = typer.Option(
        "outputs/mistral_exams",
        "--output",
        "-o",
        help="Directory to save results"
    ),
    marker_name: Optional[str] = typer.Option(
        None,
        "--marker",
        "-m",
        help="Marker name (from environment variable if not specified)"
    ),
    gui: bool = typer.Option(
        False,
        "--gui",
        "-g",
        help="Enable GUI mode"
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        help="Enable debug mode"
    )
) -> None:
    """Grade PDF exams and generate detailed feedback"""

    # .envファイルの読み込み（カレントディレクトリを優先）
    current_dotenv_path = CURRENT_DIR / ".env"
    project_dotenv_path = PROJECT_ROOT / ".env"

    if current_dotenv_path.exists():
        logger.info(f"Loading .env from current directory: {current_dotenv_path}")
        load_dotenv(dotenv_path=str(current_dotenv_path))
    elif project_dotenv_path.exists():
        logger.info(f"Loading .env from project root: {project_dotenv_path}")
        load_dotenv(dotenv_path=str(project_dotenv_path))
    else:
        logger.info("No .env file found in current or project directory. Using default load_dotenv()")
        load_dotenv()

    # ログレベルの設定
    if debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # GUIモードで実行または引数がない場合
    if gui or not pdf_paths:
        run_gui()
        return

    # APIキーのチェック（CLIモード用）
    if not check_api_keys():
        sys.exit(1)

    # 出力ディレクトリの作成
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 採点者名の設定
    marker_name = marker_name or os.getenv("MARKER_NAME", "Unknown Marker")

    # MistralExamMarkerの初期化
    marker = MistralExamMarker(
        mistral_api_key=os.getenv("MISTRAL_API_KEY", ""),
        gemini_api_key=os.getenv("GEMINI_API_KEY", ""),
        marker_name=marker_name
    )

    # 出力ディレクトリを設定
    marker.set_output_directory(output_dir)

    console.print(f"[green]Starting grading...\n")
    console.print(f"Marker: {marker_name}")
    console.print(f"Number of files to process: {len(pdf_paths)}\n")

    # PDFの処理
    try:
        for pdf_path in pdf_paths:
            asyncio.run(process_single_pdf(marker, pdf_path))

        # 統計情報の表示
        stats = marker.get_statistics()
        console.print("\n[green]Processing completed!\n")
        console.print("[bold]Statistics:")
        console.print(f"Processed files: {stats['total_exams']}")
        console.print(f"Total pages: {stats['total_pages']}")
        console.print(f"Total processing time: {stats['processing_time']:.1f} seconds")
        console.print(f"Average processing time: {stats['average_time_per_exam']:.1f} seconds/file")
        console.print(f"API calls: {stats['api_calls']}")

        console.print(f"\nResults saved to {output_dir}.")
        marker.save_all_feedback() # 全フィードバックを保存

    except Exception as e:
        console.print(f"[red]Error: {str(e)}")
        sys.exit(1)

def main():
    """プログラムのメインエントリーポイント"""
    # 引数なしで実行された場合はGUIモードで起動
    if len(sys.argv) == 1:
        run_gui()
    else:
        app()

if __name__ == "__main__":
    main()
