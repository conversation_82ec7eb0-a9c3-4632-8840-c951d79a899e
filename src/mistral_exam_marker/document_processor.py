"""
Mistral OCRを使用してPDFドキュメントを処理するモジュール
"""

import os
import json
import asyncio
import logging
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple  # Tupleをインポートに追加
import aiohttp
import base64
import fitz  # PyMuPDF
from PIL import Image
import io

# ロガーの設定
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Mistral OCRを使用したドキュメント処理クラス"""
    def __init__(self, mistral_api_key: str):
        """
        DocumentProcessorを初期化

        Args:
            mistral_api_key: Mistral AI APIキー
        """
        self.api_key = mistral_api_key
        self.max_retries = 3
        self.timeout = 30

        # OCRクライアントをインポート
        from .ocr_client import OCRClient
        self.ocr_client = OCRClient(api_key=mistral_api_key)

        # 出力ディレクトリを設定
        self.output_dir = Path("temp_mistral_ocr")
        self.output_dir.mkdir(exist_ok=True)

        # 質問番号の追跡用変数
        self._current_question_num = None
        self._last_letter = None
        self._question_sequence = []

    async def process_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        PDFを処理して、テキストと画像を抽出

        Args:
            pdf_path: 処理するPDFファイルのパス

        Returns:
            処理結果を含む辞書
        """
        try:
            # PDF文書を開く
            pdf_document = fitz.open(pdf_path)
            total_pages = len(pdf_document)
            logger.info(f"PDF loaded: {pdf_path} ({total_pages} pages)")

            # 結果を格納する辞書
            result = {
                "success": True,
                "total_pages": total_pages,
                "extracted_text": "",
                "page_texts": [],
                "embedded_images": [],
                "structured_data": {
                    "questions_and_answers": [],
                    "metadata": {}
                }
            }

            # 各ページを処理
            for page_num in range(total_pages):
                page = pdf_document[page_num]
                logger.info(f"Processing page {page_num + 1}/{total_pages}...")

                # テキストを抽出
                text = page.get_text()
                result["page_texts"].append({
                    "page_num": page_num + 1,
                    "text": text
                })
                result["extracted_text"] += f"\nPage {page_num + 1}:\n{text}"

                # 画像を抽出
                image_list = page.get_images(full=True)

                for img_index, img_info in enumerate(image_list):
                    try:
                        xref = img_info[0]
                        base_image = pdf_document.extract_image(xref)
                        image_bytes = base_image["image"]

                        # 画像をPILで開く
                        image = Image.open(io.BytesIO(image_bytes))

                        # 画像を一時保存
                        temp_path = self.output_dir / f"page_{page_num + 1}_img_{img_index + 1}.png"
                        image.save(temp_path, "PNG")

                        # Mistral OCRで画像を解析
                        ocr_result = await self.handle_mistral_api_error(self._process_image, str(temp_path))

                        if ocr_result.get("success", False):
                            result["embedded_images"].append({
                                "page_num": page_num + 1,
                                "image_index": img_index + 1,
                                "path": str(temp_path),
                                "ocr_data": ocr_result
                            })

                    except Exception as e:
                        logger.error(f"Error processing image: {str(e)}")
                        continue

                # 質問と回答のパターンを検出
                qa_pairs = self._extract_qa_pairs(text, page_num + 1)
                result["structured_data"]["questions_and_answers"].extend(qa_pairs)

            # メタデータを抽出
            metadata = {
                "title": pdf_document.metadata.get("title", ""),
                "author": pdf_document.metadata.get("author", ""),
                "creation_date": pdf_document.metadata.get("creationDate", ""),
                "modification_date": pdf_document.metadata.get("modDate", "")
            }
            result["structured_data"]["metadata"] = metadata

            pdf_document.close()
            return result

        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _process_image(self, image_path: str) -> Dict[str, Any]:
        """
        画像をMistral OCRで処理

        Args:
            image_path: 処理する画像ファイルのパス

        Returns:
            OCR処理結果を含む辞書
        """
        try:
            # すべての埋め込み画像をデフォルトでグラフとして処理
            basic_result = {
                "success": True,
                "content_type": "graph",
                "graph_type": "Economic Graph",
                "axes": self._extract_axes_from_text(""),
                "trends": [],
                "key_points": [],
                "economic_interpretation": "Basic graph interpretation pending OCR analysis"
            }

            # OCRを使用して画像のテキストコンテンツを分析
            options = {
                "detect_graphs": True,
                "detect_tables": True,
                "detect_formulas": True,
                "extract_economic_concepts": True,
                "detect_charts": True,
                "detect_diagrams": True,
                "detect_figures": True,
                "image_min_size": 100  # 小さすぎる画像を除外
            }

            result = await self.ocr_client.analyze_image(image_path, options)

            # OCRレスポンスを適切な形式に変換
            if result.get("success", False):
                raw_text = result.get("text", "")

                # 経済解釈用のテキストを準備（JSONレスポンスの場合は解析）
                economic_interpretation = self._extract_economic_interpretation(raw_text)

                # グラフタイプを判定
                content_type, graph_type = self._determine_graph_type(raw_text)

                # 軸情報を抽出
                axes = self._extract_axes_from_text(raw_text)

                # トレンド情報を抽出
                trends = self._extract_trends_from_text(raw_text)

                # キーポイントを抽出
                key_points = self._extract_key_points_from_text(raw_text)

                return {
                    "success": True,
                    "content_type": content_type,
                    "raw_text": raw_text,
                    "graph_type": graph_type,
                    "axes": axes,
                    "trends": trends,
                    "key_points": key_points,
                    "economic_interpretation": economic_interpretation
                }
            return result

        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _extract_axes_from_text(self, text: str) -> Dict[str, Any]:
        """
        テキストから軸情報を抽出

        Args:
            text: OCRテキスト

        Returns:
            軸情報を含む辞書
        """
        # デフォルトの軸情報
        axes = {
            "x_axis": {
                "label": "",
                "units": "",
                "range": {"min": None, "max": None}
            },
            "y_axis": {
                "label": "",
                "units": "",
                "range": {"min": None, "max": None}
            }
        }

        # テキストを小文字に変換
        text_lower = text.lower()

        # x軸のラベルを検出
        x_axis_patterns = [
            r"x[ -]axis:?\s*([^,\n.]+)",
            r"horizontal axis:?\s*([^,\n.]+)",
            r"on the x[ -]axis:?\s*([^,\n.]+)"
        ]

        for pattern in x_axis_patterns:
            match = re.search(pattern, text_lower)
            if match:
                axes["x_axis"]["label"] = match.group(1).strip()
                break

        # y軸のラベルを検出
        y_axis_patterns = [
            r"y[ -]axis:?\s*([^,\n.]+)",
            r"vertical axis:?\s*([^,\n.]+)",
            r"on the y[ -]axis:?\s*([^,\n.]+)"
        ]

        for pattern in y_axis_patterns:
            match = re.search(pattern, text_lower)
            if match:
                axes["y_axis"]["label"] = match.group(1).strip()
                break

        # 単位を検出
        units_patterns = {
            "currency": r"(\$|£|€|円)",
            "percentage": r"(%|percent)",
            "quantity": r"(units|pieces|items)",
            "time": r"(years|months|days|hours)"
        }

        for axis in ["x_axis", "y_axis"]:
            label = axes[axis]["label"].lower()
            for unit_type, pattern in units_patterns.items():
                if re.search(pattern, label):
                    axes[axis]["units"] = unit_type
                    break

        # 範囲を検出 (数値のみ)
        range_patterns = [
            r"range:?\s*(\d+\.?\d*)\s*(?:to|-)\s*(\d+\.?\d*)",
            r"from\s*(\d+\.?\d*)\s*(?:to|-)\s*(\d+\.?\d*)",
            r"between\s*(\d+\.?\d*)\s*(?:and|-)\s*(\d+\.?\d*)"
        ]

        for pattern in range_patterns:
            match = re.search(pattern, text_lower)
            if match:
                try:
                    min_val = float(match.group(1))
                    max_val = float(match.group(2))
                    # x軸とy軸の両方に適用 (より詳細な情報がない場合)
                    for axis in ["x_axis", "y_axis"]:
                        axes[axis]["range"] = {
                            "min": min_val,
                            "max": max_val
                        }
                except ValueError:
                    continue

        return axes

    def _extract_trends_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        テキストからトレンド情報を抽出

        Args:
            text: OCRテキスト

        Returns:
            トレンド情報のリスト
        """
        trends = []
        text_lower = text.lower()

        # トレンドを示す典型的なキーワード
        trend_indicators = {
            "増加": [
                r"increase[ds]?",
                r"rise[ds]?",
                r"grow[ts]h?",
                r"upward",
                r"higher",
                r"positive"
            ],
            "減少": [
                r"decrease[ds]?",
                r"decline[ds]?",
                r"fall[ds]?",
                r"drop[ds]?",
                r"downward",
                r"lower",
                r"negative"
            ],
            "安定": [
                r"stable",
                r"constant",
                r"steady",
                r"unchanged",
                r"flat"
            ]
        }

        # 文を分割
        sentences = re.split(r'[.!?]+', text_lower)

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            trend_info = {
                "type": None,
                "description": sentence,
                "confidence": 0.0,
                "period": None,
                "magnitude": None
            }

            # トレンドタイプを検出
            max_matches = 0
            for trend_type, patterns in trend_indicators.items():
                matches = sum(1 for pattern in patterns if re.search(pattern, sentence))
                if matches > max_matches:
                    max_matches = matches
                    trend_info["type"] = trend_type
                    trend_info["confidence"] = min(matches * 0.2, 1.0)  # 20%ずつ信頼度を上げる（最大1.0）

            # 期間を検出
            period_patterns = [
                r"(?:in|during|from|since)\s+(\d{4})",  # 年
                r"(?:in|during)\s+(q[1-4]|quarter\s+[1-4])",  # 四半期
                r"(?:in|during)\s+([a-z]+\s+\d{4})",  # 月 年
                r"over\s+the\s+past\s+(\d+)\s+(?:year|month|day)s?"  # 相対期間
            ]

            for pattern in period_patterns:
                match = re.search(pattern, sentence)
                if match:
                    trend_info["period"] = match.group(1)
                    break

            # 変化の大きさを検出
            magnitude_patterns = [
                r"(\d+\.?\d*)%",  # パーセンテージ
                r"(\d+\.?\d*)\s*(?:point|unit)s?",  # ポイントや単位
                r"(?:by|about)\s+(\d+\.?\d*)"  # 一般的な数値
            ]

            for pattern in magnitude_patterns:
                match = re.search(pattern, sentence)
                if match:
                    try:
                        trend_info["magnitude"] = float(match.group(1))
                    except ValueError:
                        continue
                    break

            if trend_info["type"]:  # トレンドタイプが検出された場合のみ追加
                trends.append(trend_info)
        # トレンド情報を文字列に変換
        trend_strings = []
        for trend in trends:
            trend_str = f"{trend['type']} trend"
            if trend.get('magnitude'):
                trend_str += f" of {trend['magnitude']}"
            if trend.get('period'):
                trend_str += f" in {trend['period']}"
            if trend.get('description'):
                trend_str += f": {trend['description']}"
            trend_strings.append(trend_str)
        return trend_strings

    def _extract_key_points_from_text(self, text: str) -> List[str]:
        """
        テキストからキーポイントを抽出

        Args:
            text: OCRテキスト

        Returns:
            キーポイントのリスト
        """
        key_points = []

        # キーポイントを示す典型的なマーカー
        markers = [
            r"key\s+point",
            r"main\s+point",
            r"important",
            r"notable",
            r"significant",
            r"highlight",
            r"conclusion",
            r"finding",
            r"observation"
        ]

        # パラグラフや文を分割
        paragraphs = text.split('\n\n')

        for para in paragraphs:
            para = para.strip()
            if not para:
                continue

            # キーポイントの可能性がある文を分割
            sentences = re.split(r'[.!?]+', para)

            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue

                # キーポイントマーカーをチェック
                is_key_point = False
                for marker in markers:
                    if re.search(marker, sentence.lower()):
                        is_key_point = True
                        break

                # 経済関連の重要な用語をチェック
                econ_terms = [
                    r"market",
                    r"demand",
                    r"supply",
                    r"price",
                    r"equilibrium",
                    r"trend",
                    r"growth",
                    r"decline",
                    r"increase",
                    r"decrease"
                ]

                term_count = sum(1 for term in econ_terms if re.search(term, sentence.lower()))
                relevance_score = term_count * 0.2  # 各用語につき20%のスコアを加算

                # キーポイントとして抽出する条件
                if is_key_point or relevance_score >= 0.4:  # 40%以上の関連性があればキーポイントとして抽出
                    key_point = {
                        "text": sentence,
                        "type": "conclusion" if "conclusion" in sentence.lower() else "observation",
                        "relevance_score": min(relevance_score, 1.0)  # スコアは最大1.0
                    }
                    key_points.append(key_point)

        # 関連性スコアでソート
        key_points.sort(key=lambda x: x["relevance_score"], reverse=True)

        # キーポイントを文字列に変換
        key_point_strings = []
        for point in key_points:
            if point.get('text'):
                # スコアが0.8以上の場合は「重要」としてマーク
                if point.get('relevance_score', 0) >= 0.8:
                    key_point_strings.append(f"Important: {point['text']}")
                else:
                    key_point_strings.append(point['text'])
        return key_point_strings

    def _determine_graph_type(self, text: str) -> Tuple[str, str]:
        """
        テキストからグラフのタイプを判定

        Args:
            text: OCRテキスト

        Returns:
            Tuple[content_type, graph_type]
        """
        text_lower = text.lower()

        # 優先度順にグラフタイプを判定
        graph_types = [
            # (キーワードリスト, content_type, graph_type)
            (["supply", "demand", "equilibrium"], "graph", "Supply and Demand Graph"),
            (["deadweight", "loss", "welfare"], "graph", "Deadweight Loss Graph"),
            (["indifference", "utility", "preference"], "graph", "Indifference Curve"),
            (["production", "possibility", "frontier", "ppf"], "graph", "Production Possibility Frontier"),
            (["cost", "marginal", "average", "total"], "graph", "Cost Curves"),
            (["is", "lm", "monetary", "fiscal"], "graph", "IS-LM Model"),
            (["phillips", "inflation", "unemployment"], "graph", "Phillips Curve"),
            (["market", "price", "quantity"], "graph", "Market Graph"),
            (["curve", "slope", "elasticity"], "graph", "Economic Curve"),
            (["axis", "coordinate", "plot"], "graph", "Economic Graph"),
            # デフォルトで全ての埋め込み画像をグラフとして扱う
            ([], "graph", "Economic Graph"),
        ]

        # すべての埋め込み画像をグラフとして扱うため、デフォルトをグラフに設定
        best_type = ("graph", "Economic Graph")

        # スコアベースでより具体的なグラフタイプを判定
        max_score = 0
        specific_type = best_type

        for keywords, content, graph_type in graph_types[:-1]:  # 最後のデフォルトエントリを除外
            score = sum(2 for kw in keywords if kw in text_lower)
            # 文全体に出現する回数でボーナススコア
            score += sum(text_lower.count(kw) - 1 for kw in keywords if kw in text_lower)

            if score > max_score:
                max_score = score
                specific_type = (content, graph_type)

        # スコアが0より大きい場合は具体的なタイプを返す
        if max_score > 0:
            return specific_type

        # その他の明確な非グラフコンテンツの判定
        if "table" in text_lower and "|" in text:
            return "table", "Data Table"

        if any(keyword in text_lower for keyword in ["equation", "formula", "=", "calculate"]):
            return "formula", "Mathematical Formula"

        # デフォルトでグラフとして扱う
        return best_type

    def _extract_economic_interpretation(self, text: str) -> str:
        """
        テキストから経済的解釈を抽出

        Args:
            text: OCRテキスト

        Returns:
            経済的解釈のテキスト
        """
        # 空のテキストをチェック
        if not text:
            return "No economic interpretation available."

        # JSONの処理
        if text and (text.startswith('{') or text.startswith('["')):
            try:
                data = json.loads(text)

                # JSON処理のエラーハンドリングを改善
                if isinstance(data, dict):
                    # 優先順位付きのキー検索
                    priority_keys = [
                        "economic_interpretation",
                        "interpretation",
                        "analysis",
                        "explanation",
                        "content",
                        "text"
                    ]

                    # Mistral OCR形式の特別処理
                    if "pages" in data and isinstance(data["pages"], list) and data["pages"]:
                        page_data = data["pages"][0]
                        if isinstance(page_data, dict):
                            if "markdown" in page_data:
                                return self._clean_markdown_text(page_data["markdown"])
                            if "text" in page_data:
                                return page_data["text"]

                    # 優先順位に基づくキー検索
                    for key in priority_keys:
                        if key in data and isinstance(data[key], str) and data[key].strip():
                            return data[key].strip()

                    # ネストされたデータの処理
                    for key in priority_keys:
                        for k, v in data.items():
                            if isinstance(v, dict) and key in v and isinstance(v[key], str):
                                return v[key].strip()

                # リスト形式のJSONの処理
                elif isinstance(data, list) and data:
                    # リストの各要素を文字列として結合
                    combined = " ".join(str(item) for item in data if item)
                    if combined.strip():
                        return combined

                # その他のJSON形式の場合
                json_str = json.dumps(data, indent=2)
                if len(json_str) > 500:  # 長すぎる場合は切り詰める
                    return json_str[:500] + "..."
                return json_str

            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON content")

        # テキストの前処理
        text = self._clean_markdown_text(text)
        text_lower = text.lower()

        # 経済的解釈を抽出するための高度なキーワードシステム
        interpretation_sections = {
            "high_priority": [
                "economic interpretation:", "economic analysis:",
                "interpretation:", "analysis of the graph:",
                "the graph illustrates:", "this diagram shows:",
            ],
            "medium_priority": [
                "explanation:", "meaning:", "understanding:",
                "this shows:", "represents:", "demonstrates:",
            ],
            "terminators": [
                "conclusion:", "summary:", "note:", "next:",
                "furthermore:", "additionally:"
            ]
        }

        # 各優先度レベルでキーワードを検索
        for priority_level in ["high_priority", "medium_priority"]:
            for keyword in interpretation_sections[priority_level]:
                if keyword in text_lower:
                    parts = text_lower.split(keyword, 1)
                    if len(parts) > 1:
                        interpretation = parts[1].strip()
                        # ターミネーターで分割
                        for terminator in interpretation_sections["terminators"]:
                            if terminator in interpretation:
                                interpretation = interpretation.split(terminator, 1)[0]
                        return self._enhance_interpretation(interpretation.capitalize())

        # キーワードが見つからない場合は、経済用語に基づいて
        # 最も関連性の高いセクションを探す
        econ_terms = ["demand", "supply", "market", "price", "quantity",
                      "equilibrium", "elasticity", "cost", "revenue", "profit"]

        best_paragraph = ""
        max_term_count = 0

        # テキストを段落に分割
        paragraphs = text.split("\n\n")
        for para in paragraphs:
            if len(para.strip()) < 10:  # 短すぎる段落はスキップ
                continue

            term_count = sum(para.lower().count(term) for term in econ_terms)
            if term_count > max_term_count:
                max_term_count = term_count
                best_paragraph = para

        # 意味のある段落が見つかれば返す
        if best_paragraph and max_term_count >= 2:
            return best_paragraph

        # それ以外の場合は元のテキストを返す（最大500文字まで）
        return text[:500] + ("..." if len(text) > 500 else "")

    def _clean_markdown_text(self, markdown_text: str) -> str:
        """マークダウンテキストを整形"""
        # 画像参照を削除
        text = re.sub(r'!\[.*?\]\(.*?\)', '', markdown_text)
        # マークダウン書式を削除
        text = re.sub(r'#{1,6}\s+', '', text)  # 見出し
        text = re.sub(r'[*_]{1,2}(.*?)[*_]{1,2}', r'\1', text)  # 強調
        text = re.sub(r'`{1,3}(.*?)`{1,3}', r'\1', text)  # コード
        # 余分な空白を整理
        text = re.sub(r'\n{3,}', '\n\n', text)
        return text.strip()

    async def handle_mistral_api_error(self, func, *args, **kwargs):
        """
        Mistral APIエラーを処理し、リトライロジックを実装する

        Args:
            func: 実行する関数
            *args: 関数の引数
            **kwargs: 関数のキーワード引数

        Returns:
            関数の実行結果
        """
        max_retries = 1
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                logger.error(f"Mistral API error: {str(e)} (attempt {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数バックオフ
                else:
                    raise

    def _extract_qa_pairs(self, text: str, page_num: int) -> List[Dict[str, Any]]:
        """
        テキストから質問と回答のペアを抽出

        Args:
            text: 解析するテキスト
            page_num: ページ番号

        Returns:
            質問と回答のペアのリスト
        """
        qa_pairs = []
        current_question = None
        current_answer = []

        lines = text.split("\n")

        # 質問番号の連続性を追跡するための変数
        last_numeric_part = None
        letter_sequence = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 質問パターンを検出
            if any(pattern in line.lower() for pattern in ["question", "q.", "q)"]):
                # 前の質問があれば保存
                if current_question:
                    qa_pairs.append({
                        "question_id": current_question["id"],
                        "question_text": current_question["text"],
                        "student_answer": "\n".join(current_answer),
                        "page_num": page_num
                    })

                # 新しい質問を開始
                q_id = self._extract_question_id(line)

                # 質問番号の追跡を更新
                if q_id.startswith("unknown_"):
                    # 不明な質問IDの場合は何もしない
                    pass
                else:
                    # 数字部分とアルファベット部分を分離
                    import re
                    match = re.match(r"(\d+)([a-zA-Z])?", q_id)
                    if match:
                        numeric_part = match.group(1)
                        letter_part = match.group(2) if match.group(2) else "a"  # アルファベットがない場合は "a" とする

                        # 質問番号を更新
                        self._current_question_num = numeric_part
                        self._last_letter = letter_part

                        # 新しい問題番号の場合、レターシーケンスをリセット
                        if numeric_part != last_numeric_part:
                            letter_sequence = [letter_part]
                            last_numeric_part = numeric_part
                        else:
                            letter_sequence.append(letter_part)

                        # 単独の数字の場合（例：「2」のみ）、質問IDを「2a」のように変換
                        if not match.group(2):
                            q_id = f"{numeric_part}a"
                            logger.info(f"Converted standalone number '{numeric_part}' to question ID '{q_id}'")
                            # 最初の小問として扱う
                            self._last_letter = "a"

                current_question = {
                    "id": q_id,
                    "text": line
                }
                current_answer = []

            # 質問IDのみのパターン
            elif self._is_question_id(line):
                if current_question:
                    qa_pairs.append({
                        "question_id": current_question["id"],
                        "question_text": current_question["text"],
                        "student_answer": "\n".join(current_answer),
                        "page_num": page_num
                    })

                q_id = self._extract_question_id(line)

                # 質問番号の追跡を更新
                if not q_id.startswith("unknown_"):
                    # 数字部分とアルファベット部分を分離
                    import re
                    match = re.match(r"(\d+)([a-zA-Z])?", q_id)
                    if match:
                        numeric_part = match.group(1)
                        letter_part = match.group(2) if match.group(2) else "a"  # アルファベットがない場合は "a" とする

                        # 質問番号を更新
                        self._current_question_num = numeric_part
                        self._last_letter = letter_part

                        # 新しい問題番号の場合、レターシーケンスをリセット
                        if numeric_part != last_numeric_part:
                            letter_sequence = [letter_part]
                            last_numeric_part = numeric_part
                        else:
                            letter_sequence.append(letter_part)

                        # 単独の数字の場合（例：「2」のみ）、質問IDを「2a」のように変換
                        if not match.group(2):
                            q_id = f"{numeric_part}a"
                            logger.info(f"Converted standalone number '{numeric_part}' to question ID '{q_id}'")
                            # 最初の小問として扱う
                            self._last_letter = "a"
                    else:
                        # 単独のアルファベットの場合
                        if len(q_id) == 1 and q_id.isalpha():
                            # 現在の問題番号が設定されている場合
                            if self._current_question_num:
                                # 質問IDを更新（例：「b」→「2b」）
                                old_q_id = q_id
                                q_id = f"{self._current_question_num}{q_id.lower()}"
                                logger.info(f"Converted standalone letter '{old_q_id}' to question ID '{q_id}'")
                                # 小問を更新
                                self._last_letter = q_id[-1]
                            else:
                                # 問題番号が不明の場合はデフォルトで問題1とする
                                self._current_question_num = "1"
                                q_id = f"1{q_id.lower()}"
                                logger.info(f"Assigned default question number to standalone letter: '{q_id}'")
                                self._last_letter = q_id[-1]
                        elif len(q_id) == 2:
                            # 質問番号を更新
                            self._current_question_num = q_id[0]
                            self._last_letter = q_id[1]

                current_question = {
                    "id": q_id,
                    "text": line
                }
                current_answer = []

            # その他の行は回答として追加
            elif current_question:
                current_answer.append(line)

        # 最後の質問を保存
        if current_question:
            qa_pairs.append({
                "question_id": current_question["id"],
                "question_text": current_question["text"],
                "student_answer": "\n".join(current_answer),
                "page_num": page_num
            })

        # 質問シーケンスを保存
        self._question_sequence = qa_pairs

        return qa_pairs

    def _extract_question_id(self, text: str) -> str:
        """
        テキストから質問IDを抽出

        Args:
            text: 解析するテキスト

        Returns:
            質問ID (例: "1a", "2b")
        """
        import re

        # 標準的な質問ID形式のパターン（大文字小文字両方対応）
        patterns = [
r"Question\s*(\d+)\s*([a-zA-Z])\)",  # Question1 a) or Question 1 a)
            r"Question (\d+[a-zA-Z])",  # Question 1a or Question 1A
            r"Q\.?(\d+[a-zA-Z])",       # Q1a, Q1A, Q.1a, or Q.1A
            r"(\d+[a-zA-Z])\)",         # 1a) or 1A)
            r"(\d+[a-zA-Z])\.",         # 1a. or 1A.
            r"Problem(\d+[a-zA-Z])",    # Problem1a or Problem1A
            r"Question(\d+[a-zA-Z])",   # Question1a or Question1A
            r"^(\d+[a-zA-Z])$",         # 1a or 1A
            r"(\d+)\s+([a-zA-Z])\)",    # 1 a) or 1 A) - 明示的に追加
            r"(\d+)\s+([a-zA-Z])\.",    # 1 a. or 1 A. - 明示的に追加
            r"(\d+)\s+([a-zA-Z]):",     # 1 a: or 1 A: - 明示的に追加
            r"^(\d+)$",                 # 単独の数字 (例: "2") - 問題番号のみの場合
            r"^(\d+)\)",                # 単独の数字 (例: "2)") - 問題番号のみの場合
            r"^(\d+)\.",                # 単独の数字 (例: "2.") - 問題番号のみの場合
            r"^Question\s+(\d+)$",      # Question 2 - 問題番号のみの場合
            r"^Q\.?\s*(\d+)$"           # Q2 or Q.2 - 問題番号のみの場合
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                # 空白で区切られた数字とアルファベットのパターン（例：1 a)）の特別処理
                if len(match.groups()) > 1 and match.group(2):
                    # 数字部分とアルファベット部分を結合
                    return f"{match.group(1)}{match.group(2).lower()}"
                else:
                    return match.group(1).lower()

        # 単独のアルファベット (a), b), c), A), B), C) など) を検出するパターン
        standalone_patterns = [
            r"^([a-zA-Z])\)$",          # a) or A)
            r"^([a-zA-Z])\.+$",         # a. or A.
            r"^([a-zA-Z])$",            # a or A
            r"^Question ([a-zA-Z])$",   # Question a or Question A
            r"^Q\.?([a-zA-Z])$"         # Qa, QA, Q.a, or Q.A
        ]

        for pattern in standalone_patterns:
            match = re.search(pattern, text.strip())
            if match:
                # 単独のアルファベットが見つかった場合、前後のコンテキストから問題番号を推測
                # この場合、クラス変数で現在の問題番号を追跡する必要がある
                # 現時点では、最後に処理した質問IDから問題番号を推測
                letter = match.group(1).lower()

                # 現在の問題番号を取得（クラス変数から）
                current_question_num = getattr(self, '_current_question_num', None)

                if current_question_num:
                    return f"{current_question_num}{letter}"
                else:
                    # 問題番号が不明の場合は、デフォルトで問題1とする
                    return f"1{letter}"

        # デフォルトIDを生成
        return f"unknown_{hash(text) % 1000:03d}"

    def _is_question_id(self, text: str) -> bool:
        """
        テキストが質問IDのみかどうかを判定

        Args:
            text: 判定するテキスト

        Returns:
            質問IDのみの場合はTrue
        """
        import re

        # 空文字列や長すぎるテキストは質問IDではない
        text = text.strip()
        if not text or len(text) > 10:
            return False

        # 標準的な質問ID形式のパターン（大文字小文字両方対応）
        patterns = [
            r"^\d+[a-zA-Z]\)?$",    # 1a or 1a), 1A or 1A)
            r"^Q\d+[a-zA-Z]$",      # Q1a or Q1A
            r"^Q\.\d+[a-zA-Z]$",    # Q.1a or Q.1A
            r"^\d+\s*[a-zA-Z]\)?$", # 1 a or 1 a), 1 A or 1 A)
            r"^\d+\s*[a-zA-Z]\.$",  # 1 a. or 1 A.
            r"^\d+\s*[a-zA-Z]:$",   # 1 a: or 1 A:
            r"^\d+$",               # 単独の数字 (例: "2") - 問題番号のみの場合
            r"^\d+\)$",             # 単独の数字 (例: "2)") - 問題番号のみの場合
            r"^\d+\.$",             # 単独の数字 (例: "2.") - 問題番号のみの場合
            r"^Question\s+\d+$",    # Question 2 - 問題番号のみの場合
            r"^Q\.?\s*\d+$"         # Q2 or Q.2 - 問題番号のみの場合
        ]

        # 単独のアルファベットのパターン（大文字小文字両方対応）
        standalone_patterns = [
            r"^[a-zA-Z]\)?$",       # a or a), A or A)
            r"^[a-zA-Z]\.$",        # a. or A.
            r"^Q\.[a-zA-Z]$",       # Q.a or Q.A
            r"^Q[a-zA-Z]$",         # Qa or QA
            r"^[a-zA-Z]$",          # a or A
            r"^[a-zA-Z]\s*\)?$",    # a ) or A )
            r"^[a-zA-Z]\s*\.$",     # a . or A .
            r"^[A-Z]\)",            # A) - 大文字のみ明示的に追加
            r"^[A-Z]\.",            # A. - 大文字のみ明示的に追加
            r"^[A-Z]$"              # A - 大文字のみ明示的に追加
        ]

        # 両方のパターンセットをチェック
        is_standard = any(re.match(pattern, text) for pattern in patterns)
        is_standalone = any(re.match(pattern, text) for pattern in standalone_patterns)

        # 単独のアルファベットの場合、現在の問題番号が設定されているかチェック
        if is_standalone and not self._current_question_num:
            # 最初の問題の場合は問題1として扱う
            self._current_question_num = "1"

        return is_standard or is_standalone
