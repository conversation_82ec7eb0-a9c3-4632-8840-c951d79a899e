"""
Module for converting OCR results and feedback into markdown format
"""

import os
from typing import Dict, Any, List
from pathlib import Path
import json
import logging
import re

logger = logging.getLogger(__name__)

class MarkdownConverter:
    """Class for converting OCR results and feedback to markdown"""

    def __init__(self):
        """Initialize MarkdownConverter"""
        self.image_dir = Path("outputs/images")
        self.image_dir.mkdir(parents=True, exist_ok=True)

    def create_feedback_document(
        self, ocr_result: Dict[str, Any], marks_and_feedback: Dict[str, Any], candidate_number: str
    ) -> str:
        """
        Generate markdown document from OCR results and grading feedback

        Args:
            ocr_result: OCR processing results
            marks_and_feedback: Grading results and feedback
            candidate_number: The student's candidate number (ID)

        Returns:
            Markdown formatted document
        """
        sections = []

        # Header
        # sections.append("# Exam Grading Results") # This line is removed
        sections.append(f"Candidate Number: {candidate_number}")
        # Removed Metadata section

        # Calculate accurate statistics by directly summing based on the criteria values
        total_marks = 0
        total_available = 0

        # Verify and log each question's scores for debugging
        for q_id, data in sorted(marks_and_feedback.items()):
            question_score = data["marks"]
            question_available = data["available_marks"]
            total_marks += question_score
            total_available += question_available
            logger.debug(f"Question {q_id}: {question_score}/{question_available} points")

        # Grading details
        sections.append("## Grading Details\n")
        for q_id, data in sorted(marks_and_feedback.items()):
            # Simplified format: Include points in the header instead of separate lines
            sections.append(f"Question {q_id} ({data['marks']}/{data['available_marks']} points)")
            # Removed "#### Feedback" line

            # Extract feedback content - exclude the header part that's already handled above
            feedback_content = data["feedback"]
            # Remove the question header and total score lines
            feedback_content = re.sub(r'^## Question \w+ Grading Results\s*', '', feedback_content)
            feedback_content = re.sub(r'^Total Score: \d+/\d+ points\s*', '', feedback_content)

            # Remove the entire "Marking Criteria Breakdown" or "Feedback" section header
            feedback_content = re.sub(r'### (?:Marking Criteria Breakdown|Feedback):\s*', '', feedback_content, flags=re.IGNORECASE)

            # Keep the numbered feedback items with scores at the end of each comment
            # This pattern preserves the format: "1. Feedback text (X/Y points)"

            # First, extract all the feedback items with their numbers and scores
            feedback_items = re.findall(r'\d+\.\s+.*?(?:\(\d+\/\d+\s+points\))', feedback_content, re.DOTALL)

            # If we found properly formatted items, use them
            if feedback_items:
                feedback_content = "\n\n".join(feedback_items)
                # Remove any empty lines that might have been created
                feedback_content = re.sub(r'\n\s*\n', '\n\n', feedback_content)
                sections.append(feedback_content)
            else:
                # If no properly formatted items were found, use the original content
                # but don't add the total score at the end since we want individual scores
                sections.append(feedback_content)

            sections.append("")  # Add empty line

        # Marker名とTotal ScoreをGrading Detailsの後に追加
        sections.append(f"\nTotal Score: {total_marks}/{total_available}")
        sections.append("Marker: Tomoto Masuda\n")

        # Chart analysis
        if ocr_result.get("embedded_images"):
            sections.append("## Chart Analysis\n")
            for img_data in ocr_result["embedded_images"]:
                ocr_data = img_data.get("ocr_data", {})
                if ocr_data.get("success"):
                    sections.append(f"Page {img_data['page_num']} Chart\n") # Removed ###

                    # Chart type and features
                    if "graph_type" in ocr_data:
                        sections.append(f"- Type: {ocr_data['graph_type']}")

                    # Axis information
                    axes = ocr_data.get("axes", {})
                    if axes:
                        sections.append("- Axes:")
                        sections.append(f"  - X-axis: {axes.get('x', 'Unknown')}")
                        sections.append(f"  - Y-axis: {axes.get('y', 'Unknown')}")

                    # Economic interpretation
                    if "economic_interpretation" in ocr_data:
                        sections.append("\nEconomic Interpretation") # Removed ####
                        sections.append(ocr_data["economic_interpretation"])

                    # Detected key points
                    key_points = ocr_data.get("key_points", [])
                    if key_points:
                        sections.append("\nKey Points") # Removed ####
                        for point in key_points:
                            sections.append(f"- {point}")

                    sections.append("")  # Add empty line

        return "\n".join(sections)

    def create_ocr_report(self, ocr_result: Dict[str, Any]) -> str:
        """
        Generate detailed OCR processing report

        Args:
            ocr_result: OCR processing results

        Returns:
            Markdown formatted OCR report
        """
        sections = []

        # Header
        sections.append("# OCR Processing Report\n")

        # Basic information
        sections.append("## Basic Information")
        sections.append(f"- Total Pages: {ocr_result.get('total_pages', 0)}")
        sections.append(f"- Processed Images: {len(ocr_result.get('embedded_images', []))}\n")

        # Metadata
        metadata = ocr_result.get("structured_data", {}).get("metadata", {})
        if metadata:
            sections.append("## Metadata")
            sections.append("```yaml")
            for key, value in metadata.items():
                if value:
                    sections.append(f"{key}: {value}")
            sections.append("```\n")

        # Questions and answers structure
        qa_pairs = ocr_result.get("structured_data", {}).get("questions_and_answers", [])
        if qa_pairs:
            sections.append("## Detected Questions and Answers\n")
            for qa in qa_pairs:
                sections.append(f"### Question {qa.get('question_id', 'Unknown')}")
                sections.append(f"Page: {qa.get('page_num', 'Unknown')}\n")
                sections.append("Question:")
                sections.append(f"```\n{qa.get('question_text', 'Unknown')}\n```\n")
                sections.append("Answer:")
                sections.append(f"```\n{qa.get('student_answer', 'Unknown')}\n```\n")

        # Image analysis results
        images = ocr_result.get("embedded_images", [])
        if images:
            sections.append("## Image Analysis Results\n")
            for img in images:
                sections.append(f"### Page {img.get('page_num', 'Unknown')} Image {img.get('image_index', 'Unknown')}")

                ocr_data = img.get("ocr_data", {})
                if ocr_data.get("success"):
                    # Image type and features
                    sections.append("\n#### Image Features")
                    if "content_type" in ocr_data:
                        sections.append(f"- Type: {ocr_data['content_type']}")

                    # Detected text
                    if "extracted_text" in ocr_data:
                        sections.append("\n#### Detected Text")
                        sections.append("```")
                        sections.append(ocr_data["extracted_text"])
                        sections.append("```")

                    # Graph information
                    if ocr_data.get("content_type") == "graph":
                        sections.append("\n#### Graph Information")
                        if "graph_type" in ocr_data:
                            sections.append(f"- Graph Type: {ocr_data['graph_type']}")

                        axes = ocr_data.get("axes", {})
                        if axes:
                            sections.append("- Axes:")
                            sections.append(f"  - X-axis: {axes.get('x', 'Unknown')}")
                            sections.append(f"  - Y-axis: {axes.get('y', 'Unknown')}")

                        if "trends" in ocr_data:
                            sections.append("\n##### Detected Trends")
                            for trend in ocr_data["trends"]:
                                sections.append(f"- {trend}")

                        if "economic_interpretation" in ocr_data:
                            sections.append("\n##### Economic Interpretation")
                            sections.append(ocr_data["economic_interpretation"])
                else:
                    sections.append("\n**Image processing failed**")
                    if "error" in ocr_data:
                        sections.append(f"Error: {ocr_data['error']}")

                sections.append("")  # Add empty line

        # Text by page
        page_texts = ocr_result.get("page_texts", [])
        if page_texts:
            sections.append("## Text by Page\n")
            for page in page_texts:
                sections.append(f"### Page {page.get('page_num', 'Unknown')}")
                sections.append("```")
                sections.append(page.get("text", "").strip())
                sections.append("```\n")

        return "\n".join(sections)

    def format_feedback(self, feedback_data: Dict[str, Any]) -> str:
        """
        Format grading feedback into markdown

        Args:
            feedback_data: Feedback data

        Returns:
            Markdown formatted feedback
        """
        sections = []

        # Header
        sections.append(f"### {feedback_data.get('title', 'Grading Feedback')}\n")

        # Score information
        if "marks" in feedback_data:
            sections.append(f"**Score**: {feedback_data['marks']} / {feedback_data.get('total_marks', '?')}\n")

        # Main feedback
        if "feedback" in feedback_data:
            sections.append("#### Feedback")
            sections.append(feedback_data["feedback"])
            sections.append("")

        # Analysis results
        analysis = feedback_data.get("analysis", {})
        if analysis:
            sections.append("#### Analysis")
            if "key_points" in analysis:
                sections.append("**Key Points**:")
                sections.append(analysis["key_points"])
                sections.append("")

            if "strengths" in analysis:
                sections.append("**Strengths**:")
                sections.append(analysis["strengths"])
                sections.append("")

            if "weaknesses" in analysis:
                sections.append("**Areas for Improvement**:")
                sections.append(analysis["weaknesses"])
                sections.append("")

        # Additional comments
        if "additional_comments" in feedback_data:
            sections.append("#### Additional Comments")
            sections.append(feedback_data["additional_comments"])

        return "\n".join(sections)
