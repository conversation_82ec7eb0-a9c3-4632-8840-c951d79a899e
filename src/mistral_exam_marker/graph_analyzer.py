import os
import json
import logging
import asyncio
import google.generativeai as genai
from google.generativeai import types
import PIL.Image
from pathlib import Path
import io
import fitz  # PyMuPDF
import math
import numpy as np
import re
from typing import Dict, Any, Union, Tuple, Optional, List
from dataclasses import dataclass

@dataclass
class QuestionMapping:
    question_id: str  # 例: "QUESTION_1c"
    image_paths: List[str]  # 関連する画像ファイルのパス
    analysis_results: List[Dict]  # 各画像の分析結果

class GraphAnalyzer:
    def __init__(self, api_key: str):
        # ロガーの初期化
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)

        # Gemini APIクライアントの初期化
        genai.configure(api_key=api_key)
        self.client = genai

        # 設問と画像の紐付けを管理
        self.question_mappings: Dict[str, QuestionMapping] = {}

    async def map_images_to_questions(self, questions: Dict[str, str], image_dir: str) -> Dict[str, QuestionMapping]:
        """
        問題とイメージの紐付けを行う
        各グラフは最も関連性スコアの高い設問にのみ割り当てられる
        """
        try:
            # 画像ファイルの取得と検証
            image_files = []  # [(ファイル名, パス)]のリスト
            for f in os.listdir(image_dir):
                if f.endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(image_dir, f)
                    try:
                        with PIL.Image.open(img_path) as img:
                            # 基本的な画像検証を緩和
                            if img.width >= 50 and img.height >= 50:  # サイズ制限を緩和
                                image_files.append((f, img_path))
                            else:
                                self.logger.warning(f"Skipping {f}: Image too small")
                    except Exception as e:
                        self.logger.error(f"Error validating {f}: {str(e)}")

            # 画像ごとの分析結果とスコアを保存
            image_scores: Dict[str, List[Tuple[str, float, Dict, str]]] = {}  # img_file -> [(question_id, score, analysis, img_path)]

            # 全ての画像を分析
            for img_file, img_path in image_files:
                try:
                    img = PIL.Image.open(img_path)
                    question_scores = []  # この画像の各設問に対するスコア

                    # 各設問に対してスコアを計算
                    for question_id, question_text in questions.items():
                        if not question_id.startswith("QUESTION_"):
                            continue

                        question_type = self._determine_question_type(question_text)
                        enhanced_context = self._enhance_question_context(question_text, question_type)
                        analysis = await self.analyze_graph(img, context=enhanced_context)

                        if not analysis:
                            continue

                        # コンテンツタイプの検証(より柔軟に)
                        if not self._validate_content_type(analysis, question_type):
                            if self._could_be_graph(analysis):
                                analysis['content_type'] = 'graph'
                            else:
                                continue

                        # スコアの計算
                        relevance_score = self._calculate_relevance_score(analysis, question_text)
                        quality_score = self._assess_graph_quality(analysis)
                        combined_score = self._calculate_combined_score(
                            relevance_score,
                            quality_score,
                            analysis
                        )

                        if combined_score >= 0.5:
                            analysis.update({
                                'relevance_score': relevance_score,
                                'quality_score': quality_score,
                                'combined_score': combined_score
                            })
                            question_scores.append((question_id, combined_score, analysis, img_path))

                    # スコアが存在する場合のみ保存
                    if question_scores:
                        image_scores[img_file] = question_scores

                except Exception as e:
                    self.logger.error(f"Error analyzing {img_file}: {str(e)}")
                    continue

            # 画像の割り当て処理のリセット
            self.question_mappings = {}
            assigned_images = set()  # 既に割り当て済みの画像を追跡

            # 全てのスコアを一つのリストにまとめてソート
            all_scores = []
            for img_file, scores in image_scores.items():
                for question_id, score, analysis, img_path in scores:
                    all_scores.append((score, img_file, question_id, analysis, img_path))

            # スコアの高い順にソート
            all_scores.sort(reverse=True)

            # 高スコアの順に割り当て
            for score, img_file, question_id, analysis, img_path in all_scores:
                # 既に割り当て済みの画像はスキップ
                if img_file in assigned_images:
                    continue

                # 設問のマッピングを更新または作成
                if question_id not in self.question_mappings:
                    self.question_mappings[question_id] = QuestionMapping(
                        question_id=question_id,
                        image_paths=[img_path],
                        analysis_results=[analysis]
                    )
                else:
                    # 同じ設問に複数のグラフを割り当てることは許可
                    self.question_mappings[question_id].image_paths.append(img_path)
                    self.question_mappings[question_id].analysis_results.append(analysis)

                assigned_images.add(img_file)  # 画像を割り当て済みとしてマーク
                self.logger.info(f"Mapped {img_file} to {question_id} with score {score}")

            return self.question_mappings

        except Exception as e:
            self.logger.error(f"Error in map_images_to_questions: {str(e)}")
            raise

    def _could_be_graph(self, analysis: Dict) -> bool:
        """
        画像がグラフとして扱える可能性があるか判定
        """
        # コンテンツタイプが明示的にグラフ
        if analysis.get('content_type') == 'graph':
            return True

        # 軸情報が存在
        axes = analysis.get('axes', {})
        if axes.get('x') or axes.get('y'):
            return True

        # トレンド情報が存在
        trends = analysis.get('trends', [])
        if trends and isinstance(trends, list) and len(trends) > 0:
            return True

        # 経済的な解釈が存在
        econ_interp = analysis.get('economic_interpretation', {})
        if isinstance(econ_interp, dict) and (
            econ_interp.get('main_concepts') or
            econ_interp.get('relationships')
        ):
            return True

        return False

    def _determine_question_type(self, question_text: str) -> str:
        """
        問題タイプを判定する(グラフ・数式・一般テキスト)
        """
        # グラフ関連のキーワード(拡充)
        graph_keywords = {
            'graph', 'plot', 'curve', 'diagram', 'chart',
            'supply', 'demand', 'equilibrium', 'slope',
            'axis', 'axes', 'line', 'point', 'intersection',
            'coordinate', 'trend', 'shift', 'movement',
            'increase', 'decrease', 'change', 'draw',
            'illustrate', 'show', 'demonstrate', 'represent',
            'relationship', 'correlation', 'function'
        }

        # 数式関連のキーワード
        formula_keywords = {
            'equation', 'formula', 'calculate', 'solve',
            'let', 'given', 'find', '=', '+', '-', '*', '/',
            'value', 'number', 'sum', 'total', 'difference'
        }

        question_lower = question_text.lower()

        # キーワードのスコアリングを改善
        graph_score = sum(2 if kw in question_lower else 0 for kw in graph_keywords)
        formula_score = sum(1 if kw in question_lower else 0 for kw in formula_keywords)

        # 経済的文脈も考慮
        if any(term in question_lower for term in ['market', 'price', 'quantity', 'cost']):
            graph_score += 2

        if graph_score > formula_score:
            return 'graph'
        elif formula_score > graph_score:
            return 'formula'
        else:
            # デフォルトでグラフとして扱う
            return 'graph'

    def _enhance_question_context(self, question_text: str, question_type: str) -> str:
        """
        問題のコンテキストを拡張して分析精度を向上
        """
        if question_type == 'graph':
            return f"Analyze this economic graph or diagram considering: {question_text}"
        elif question_type == 'formula':
            return f"Examine this mathematical expression in economic context: {question_text}"
        else:
            return f"Analyze this image in economic context: {question_text}"

    def _validate_content_type(self, analysis: Dict, expected_type: str) -> bool:
        """
        分析結果のコンテンツタイプを検証(より柔軟に)
        """
        content_type = analysis.get('content_type', '').lower()

        if expected_type == 'graph':
            # グラフとして扱える可能性のある他のタイプも許可
            return content_type in ['graph', 'chart', 'diagram', 'plot'] or self._could_be_graph(analysis)
        elif expected_type == 'formula':
            return content_type in ['formula', 'equation', 'math']

        return True

    def _calculate_combined_score(self, relevance_score: float, quality_score: float, analysis: Dict) -> float:
        """
        関連性スコアと品質スコアを統合(より寛容に)
        """
        # 基本的なスコアの重み付け
        weights = {
            'relevance': 0.5,  # 関連性の重みを増加
            'quality': 0.3,
            'confidence': 0.2   # 信頼度の重みを減少
        }

        # 信頼度スコア
        confidence_score = float(analysis.get('analysis_confidence', 0.0))

        # エラーフラグのペナルティを軽減
        error_flags = analysis.get('error_flags', [])
        error_penalty = len(error_flags) * 0.05  # ペナルティを半分に

        # 最終スコアの計算
        combined_score = (
            weights['relevance'] * relevance_score +
            weights['quality'] * quality_score +
            weights['confidence'] * confidence_score
        )

        # ペナルティの適用
        combined_score = max(0.0, combined_score - error_penalty)

        return min(1.0, combined_score)

    def _calculate_relevance_score(self, analysis: Dict, question_text: str) -> float:
        """
        Calculate relevance score between graph analysis and question text
        """
        score = 0.0
        graph_type = analysis.get('graph_type', '').lower()
        question_lower = question_text.lower()

        # Graph type matching(より包括的に)
        graph_type_mappings = {
            'supply and demand': ['supply', 'demand', 'equilibrium', 'price', 'quantity', 'market'],
            'cost curves': ['cost', 'marginal', 'average', 'total', 'production'],
            'production possibilities': ['production', 'possibility', 'trade-off', 'frontier', 'ppf'],
            'indifference curves': ['indifference', 'utility', 'preference', 'satisfaction'],
            'is-lm': ['is-lm', 'interest rate', 'investment', 'money supply', 'monetary'],
            'phillips curve': ['phillips', 'inflation', 'unemployment', 'trade-off'],
            'market graph': ['market', 'price', 'quantity', 'equilibrium'],
            'economic curve': ['curve', 'slope', 'elasticity', 'relationship']
        }

        # Check graph type relevance(スコアリングを改善)
        for graph_pattern, keywords in graph_type_mappings.items():
            if graph_pattern in graph_type:
                score += 0.5 * sum(1 for keyword in keywords if keyword in question_lower)

        # Axis label matching(より柔軟に)
        axes = analysis.get('axes', {})
        x_label = axes.get('x', '').lower()
        y_label = axes.get('y', '').lower()

        axis_keywords = {
            'price': ['price', 'cost', 'revenue', 'value', 'payment'],
            'quantity': ['quantity', 'output', 'production', 'volume', 'units'],
            'income': ['income', 'gdp', 'output', 'revenue', 'earnings'],
            'interest rate': ['interest', 'rate', 'investment', 'return']
        }

        # Check axis label relevance
        for label, keywords in axis_keywords.items():
            if any(kw in x_label or kw in y_label for kw in keywords):
                score += 0.4 * sum(1 for keyword in keywords if keyword in question_lower)

        # Trend analysis(より包括的に)
        trends = analysis.get('trends', [])
        trend_keywords = [
            'increase', 'decrease', 'shift', 'change', 'relationship',
            'movement', 'trend', 'pattern', 'behavior', 'direction'
        ]
        score += 0.3 * sum(1 for keyword in trend_keywords if keyword in question_lower)

        # 経済的コンテキストのボーナス
        economic_keywords = [
            'market', 'equilibrium', 'supply', 'demand', 'price',
            'quantity', 'cost', 'revenue', 'profit', 'elasticity'
        ]
        score += 0.2 * sum(1 for keyword in economic_keywords if keyword in question_lower)

        # Normalize score to 0-1 range
        return min(max(score, 0.0), 1.0)

    def _assess_graph_quality(self, analysis: Dict) -> float:
        """
        グラフの品質評価(より寛容に)
        """
        quality_score = 0.0

        # Axis label evaluation (30%)
        axes = analysis.get('axes', {})
        x_label = axes.get('x', '').strip().lower()
        y_label = axes.get('y', '').strip().lower()

        economic_terms = {
            'price', 'quantity', 'cost', 'revenue', 'supply', 'demand',
            'income', 'output', 'rate', 'value', 'units', 'level'
        }

        # 軸ラベルの評価をより柔軟に
        if x_label or y_label:  # いずれかの軸が存在すれば部分的にスコア付与
            if any(term in x_label for term in economic_terms):
                quality_score += 0.15
            if any(term in y_label for term in economic_terms):
                quality_score += 0.15

        # Trend analysis (35%)
        trends = analysis.get('trends', [])
        if trends and isinstance(trends, list):
            valid_trends = [t for t in trends if isinstance(t, str) and len(t.strip()) > 5]
            trend_scores = []
            for t in valid_trends[:3]:
                base_score = 0.15
                if any(term in t.lower() for term in ['increase', 'decrease', 'shift']):
                    base_score += 0.05
                if any(term in t.lower() for term in ['because', 'due to', 'leads to']):
                    base_score += 0.05
                trend_scores.append(base_score)
            trend_score = sum(trend_scores)
            quality_score += min(trend_score, 0.35)

        # Economic interpretation (35%)
        interpretation = analysis.get('economic_interpretation', '')
        if interpretation and isinstance(interpretation, str):
            words = interpretation.lower().split()

            # 文章の長さに基づく基本スコア (0-0.15)
            length_score = min(len(words) / 30.0, 1.0) * 0.15

            # 経済概念の評価 (0-0.20)
            concepts = {
                'primary': {'equilibrium', 'market', 'supply', 'demand', 'price', 'quantity'},
                'secondary': {'effect', 'impact', 'elasticity', 'change', 'relationship'}
            }

            concept_score = 0.0
            primary_matches = sum(1 for c in concepts['primary'] if any(c in w for w in words))
            secondary_matches = sum(1 for c in concepts['secondary'] if any(c in w for w in words))
            concept_score = (primary_matches * 0.025 + secondary_matches * 0.0125)

            quality_score += length_score + min(concept_score, 0.20)

        return min(max(quality_score, 0.0), 1.0)

    def _calculate_quality_metrics(self, analysis: Dict) -> Dict[str, float]:
        """
        分析結果の品質メトリクスを計算し、エラーチェックを強化
        """
        metrics = {
            'content_quality': 0.0,
            'mathematical_accuracy': 0.0,
            'economic_relevance': 0.0,
            'presentation_clarity': 0.0,
            'overall_confidence': 0.0,
            'error_flags': []
        }

        try:
            # 基本的な入力検証
            if not isinstance(analysis, dict):
                raise ValueError("Invalid analysis format")

            # コンテンツタイプの検証と評価 (25%)
            content_type = analysis.get('content_type', '').lower()
            if not content_type:
                metrics['error_flags'].append('missing_content_type')
            elif content_type in ['graph', 'formula', 'chart', 'diagram', 'plot']:  # より多くのタイプを許可
                metrics['content_quality'] += 0.25

                # コンテンツ固有の検証をより柔軟に
                if content_type in ['graph', 'chart', 'diagram', 'plot']:
                    axes = analysis.get('axes', {})
                    if not (axes.get('x') or axes.get('y')):  # いずれかの軸があれば可
                        metrics['error_flags'].append('missing_axes_labels')
                    else:
                        metrics['content_quality'] += 0.25
                elif content_type == 'formula':
                    math_elements = analysis.get('mathematical_elements', {})
                    if not (math_elements.get('equations') or math_elements.get('variables')):
                        metrics['error_flags'].append('missing_math_elements')
                    else:
                        metrics['content_quality'] += 0.25

            # 数学的な正確性の評価 (25%)
            if content_type in ['graph', 'chart', 'diagram', 'plot']:
                trends = analysis.get('trends', [])
                if trends and isinstance(trends, list):
                    metrics['mathematical_accuracy'] = min(len(trends) * 0.15, 0.25)
            elif content_type == 'formula':
                math_elements = analysis.get('mathematical_elements', {})
                if math_elements:
                    metrics['mathematical_accuracy'] = min(
                        len(math_elements.get('equations', [])) * 0.15 +
                        len(math_elements.get('variables', [])) * 0.1,
                        0.25
                    )

            # 経済的関連性の評価 (25%)
            econ_interp = analysis.get('economic_interpretation', {})
            if isinstance(econ_interp, dict):
                metrics['economic_relevance'] = min(
                    len(econ_interp.get('main_concepts', [])) * 0.15 +
                    len(econ_interp.get('relationships', [])) * 0.1,
                    0.25
                )

            # プレゼンテーションの明確性 (25%)
            quality = analysis.get('quality_assessment', {})
            if isinstance(quality, dict):
                metrics['presentation_clarity'] = min(
                    quality.get('clarity', 0.0) * 0.1 +
                    quality.get('completeness', 0.0) * 0.1 +
                    quality.get('economic_relevance', 0.0) * 0.05,
                    0.25
                )

            # 全体的な信頼度(デフォルト値を設定)
            metrics['overall_confidence'] = float(analysis.get('analysis_confidence', 0.7))

        except Exception as e:
            self.logger.error(f"Error calculating quality metrics: {str(e)}")
            metrics['error_flags'].append(f"calculation_error: {str(e)}")

        # 全てのメトリクスを0-1の範囲に正規化
        for key in metrics:
            if key != 'error_flags':
                metrics[key] = min(max(metrics[key], 0.0), 1.0)

        return metrics

    def get_question_mappings(self) -> Dict[str, QuestionMapping]:
        """
        設問と画像の紐付け情報を取得
        """
        return self.question_mappings

    def get_images_for_question(self, question_id: str) -> List[str]:
        """
        特定の設問に関連する画像パスを取得
        """
        mapping = self.question_mappings.get(question_id)
        return mapping.image_paths if mapping else []

    def get_analysis_for_question(self, question_id: str) -> List[Dict]:
        """
        特定の設問に関連する画像分析結果を取得
        """
        mapping = self.question_mappings.get(question_id)
        return mapping.analysis_results if mapping else []

    async def analyze_graph(self, image: PIL.Image.Image, context: str = "", max_retries: int = 3) -> Dict[str, Any]:
        """
        Analyze economic graph with enhanced accuracy and detail
        """
        retry_count = 0
        base_wait_time = 1  # 初期待機時間(秒)

        while retry_count <= max_retries:
            try:
                # プロンプトの準備
                prompt = f"""Analyze the following economic image in detail:

Context: {context}

Provide a comprehensive analysis in the following JSON format:
{{
    "success": boolean,
    "content_type": "graph" or "formula" or "other",
    "graph_type": "specific type of economic graph (if applicable)",
    "axes": {{
        "x": "x-axis label with units if present",
        "y": "y-axis label with units if present"
    }},
    "mathematical_elements": {{
        "equations": ["list of equations if present"],
        "variables": ["list of variables and their meanings"],
        "relationships": ["mathematical relationships described"]
    }},
    "trends": [{{
        "description": "detailed trend description",
        "direction": "increase/decrease/shift",
        "cause_effect": "explanation of causality if present"
    }}],
    "economic_interpretation": {{
        "main_concepts": ["primary economic concepts identified"],
        "relationships": ["economic relationships explained"],
        "implications": "broader economic implications"
    }},
    "quality_assessment": {{
        "clarity": float (0-1),
        "completeness": float (0-1),
        "economic_relevance": float (0-1),
        "issues": ["list of quality concerns"]
    }},
    "analysis_confidence": float (0-1)
}}

Key Analysis Points:
1. Look for any visual patterns that could indicate economic relationships
2. Consider both graphical elements and any text/numbers present
3. Identify potential economic meanings even if not explicitly labeled
4. Note any trends or patterns that might be economically significant
5. Consider multiple possible interpretations"""

                # 画像のバイト変換
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format='PNG')
                img_byte_arr = img_byte_arr.getvalue()

                # アップロードとコンテンツ準備
                uploaded_file = genai.upload_file(file=img_byte_arr)
                contents = [
                    types.Content(
                        role="user",
                        parts=[
                            types.Part.from_uri(
                                file_uri=uploaded_file.uri,
                                mime_type=uploaded_file.mime_type,
                            ),
                            types.Part.from_text(text=prompt),
                        ],
                    ),
                ]

                # 生成設定
                generate_content_config = types.GenerateContentConfig(
                    temperature=0.1,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=2048,
                    response_mime_type="text/plain",
                )

                # レスポンス処理と検証
                response_text = ""
                try:
                    for chunk in self.client.models.generate_content_stream(
                        model="gemini-2.5-flash-preview-05-20",
                        contents=contents,
                        config=generate_content_config,
                    ):
                        response_text += chunk.text

                    # レスポンスの基本検証
                    if not response_text:
                        raise ValueError("Empty response received")

                    # JSONの抽出と解析
                    json_match = re.search(r'```(?:json)?\s*({[\s\S]*?})\s*```', response_text)
                    if not json_match:
                        # JSONが見つからない場合、テキスト全体を解析
                        analysis = {
                            "success": True,
                            "content_type": "graph",  # デフォルトでグラフとして扱う
                            "raw_text": response_text,
                            "analysis_confidence": 0.7
                        }
                    else:
                        analysis = json.loads(json_match.group(1).strip())

                    # 品質メトリクスの計算と追加
                    quality_metrics = self._calculate_quality_metrics(analysis)
                    analysis['quality_metrics'] = quality_metrics

                    return analysis

                except json.JSONDecodeError as e:
                    raise ValueError(f"Invalid JSON format: {str(e)}")
                except Exception as e:
                    raise ValueError(f"Response processing error: {str(e)}")

            except Exception as e:
                error_str = str(e)
                self.logger.warning(f"Attempt {retry_count + 1} failed: {error_str}")

                # 429 You exceeded your current quota エラーの特別処理（一度だけリトライ）
                if "429 You exceeded your current quota" in error_str and retry_count == 0:
                    self.logger.warning("Quota exceeded error detected. Waiting 30 seconds before retrying...")
                    await asyncio.sleep(30)  # 30秒待機
                    self.logger.info("Retrying API call after quota error...")
                    retry_count += 1
                    continue
                # その他のレート制限エラーの場合
                elif "429" in error_str and retry_count < max_retries:
                    wait_time = base_wait_time * (2 ** retry_count)
                    self.logger.info(f"Rate limit exceeded. Waiting {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                    retry_count += 1
                    continue

                # 最後の試行または他のエラーの場合
                if retry_count == max_retries or "429" not in error_str:
                    self.logger.error(f"Final attempt failed: {error_str}")
                    return {
                        "success": False,
                        "error": error_str,
                        "retry_count": retry_count
                    }

                retry_count += 1

        return {
            "success": False,
            "error": "Maximum retries exceeded",
            "retry_count": max_retries
        }
