"""
Mistral OCR APIクライアントモジュール
"""

from mistralai import Mistral
import os
import json
import asyncio
import aiohttp
import logging
from typing import Dict, Any, Optional, List, Generator
import base64
from pathlib import Path
from functools import partial
from io import BytesIO
import uuid
import sys
import subprocess

# PDF画像変換ライブラリの追加
PDF_CONVERSION_AVAILABLE = False
try:
    from pdf2image import convert_from_bytes, convert_from_path
    PDF_CONVERSION_AVAILABLE = True
except ImportError:
    import platform
    os_type = platform.system()
    
    # OSごとのインストール手順を表示
    if os_type == 'Darwin':  # macOS
        logging.warning(
            "pdf2image library not found. Please install it and its dependencies:\n"
            "1. Install poppler: brew install poppler\n"
            "2. Install pdf2image: pip install pdf2image\n"
            "Detail: https://github.com/Belval/pdf2image#macos"
        )
    elif os_type == 'Linux':
        logging.warning(
            "pdf2image library not found. Please install it and its dependencies:\n"
            "1. Install poppler-utils: sudo apt-get install poppler-utils\n"
            "2. Install pdf2image: pip install pdf2image\n"
            "Detail: https://github.com/Belval/pdf2image#linux"
        )
    elif os_type == 'Windows':
        logging.warning(
            "pdf2image library not found. Please install it and its dependencies:\n"
            "1. Download poppler for Windows: https://github.com/oschwartz10612/poppler-windows/releases\n"
            "2. Add the bin folder to your PATH\n"
            "3. Install pdf2image: pip install pdf2image\n"
            "Detail: https://github.com/Belval/pdf2image#windows"
        )
    else:
        logging.warning("pdf2image library not found. Install with: pip install pdf2image")
    
    # 代替として PyMuPDF を試す
    try:
        import fitz  # PyMuPDF
        logging.info("Using PyMuPDF as an alternative to pdf2image")
        PDF_CONVERSION_AVAILABLE = True
    except ImportError:
        logging.warning("PyMuPDF not found either. PDF processing will be limited. Install with: pip install PyMuPDF")

logger = logging.getLogger(__name__)

class OCRClient:
    """Mistral OCR APIクライアント"""
    
    def __init__(self, api_key: str, client_id: Optional[str] = None):
        """
        OCRClientを初期化
        
        Args:
            api_key: Mistral AI APIキー
            client_id: クライアント識別子(省略時は自動生成)
        """
        self.api_key = api_key
        # 新しいMistralクライアント
        self.client = Mistral(api_key=api_key)
        
        self.max_retries = 3
        self.timeout = aiohttp.ClientTimeout(total=30)
        
        # クライアントIDがない場合は生成
        self.client_id = client_id or f"exam-marker-{str(uuid.uuid4())[:8]}"
        
        # 標準的なヘッダー
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # サポートされているファイル形式
        self.SUPPORTED_TYPES = {
            'pdf': 'application/pdf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg'
        }

    async def analyze_document(
        self, file_path: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        ドキュメントを分析
        
        Args:
            file_path: 分析するファイルのパス
            options: 分析オプション
            
        Returns:
            分析結果
        """
        try:
            # PDF処理の最適化: ファイル拡張子がPDFの場合、画像に変換して処理
            file_ext = Path(file_path).suffix.lower().lstrip('.')
            
            if file_ext == 'pdf' and PDF_CONVERSION_AVAILABLE:
                logger.info(f"PDFを画像に変換して処理: {file_path}")
                return await self._process_pdf_as_images(file_path, options)
            
            # 新しいOCR APIを使用
            with open(file_path, "rb") as f:
                file_data = f.read()
            
            response = await self.client.ocr.process(
                model="mistral-ocr-latest",
                document={
                    "type": "document_url",
                    "document_url": {
                        "url": f"data:{self.SUPPORTED_TYPES.get(file_ext, 'application/pdf')};base64,{base64.b64encode(file_data).decode()}"
                    }
                },
                pages=options.get("pages") if options else None,
                include_image_base64=options.get("include_image_base64", False) if options else False,
                image_limit=options.get("image_limit") if options else None,
                image_min_size=options.get("image_min_size") if options else None
            )
            
            # レスポンスを待機してから変換
            if isinstance(response, str):
                result = {"text": response}
            else:
                result = await response.json() if hasattr(response, 'json') else response

            return {
                "success": True,
                "text": result["text"] if isinstance(result, dict) else result,
                "tables": result.get("tables", []) if isinstance(result, dict) else [],
                "formulas": result.get("formulas", []) if isinstance(result, dict) else []
            }
            
        except Exception as e:
            logger.error(f"Error occurred while analyzing document: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _process_pdf_as_images(
        self, pdf_path: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        PDFを画像に変換して処理
        
        Args:
            pdf_path: 分析するPDFファイルのパス
            options: 分析オプション
            
        Returns:
            分析結果(すべてのページの結果を統合)
        """
        try:
            images = []
            
            # pdf2imageが利用可能な場合はそれを使用
            if 'convert_from_path' in globals():
                try:
                    images = convert_from_path(
                        pdf_path, 
                        dpi=300,  # 高解像度でスキャン
                        fmt="jpeg",
                        grayscale=False,
                        transparent=False
                    )
                except Exception as e:
                    logger.error(f"PDF to image conversion failed: {str(e)}")
                    
            # PyMuPDFを代替として使用
            elif 'fitz' in sys.modules:
                try:
                    pdf_document = fitz.open(pdf_path)
                    for page_num in range(len(pdf_document)):
                        page = pdf_document.load_page(page_num)
                        pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))  # 300dpi
                        img_data = BytesIO(pix.tobytes("jpeg"))
                        
                        # PILのImageを作成(結果の統一性のため)
                        from PIL import Image
                        img = Image.open(img_data)
                        images.append(img)
                except Exception as e:
                    logger.error(f"PyMuPDF PDF conversion failed: {str(e)}")
            
            # 変換に失敗した場合はエラーを返す
            if not images:
                return {
                    "success": False,
                    "error": "PDF conversion failed. Please install pdf2image or PyMuPDF."
                }
            
            results = []
            
            # 各ページを個別に処理
            for i, image in enumerate(images):
                logger.info(f"PDF {pdf_path} のページ {i+1}/{len(images)} を処理中...")
                
                # 画像をバイトストリームに変換
                buffer = BytesIO()
                image.save(buffer, format="JPEG", quality=85)
                image_data = base64.b64encode(buffer.getvalue()).decode()
                
                # 機能リストの作成 (新しい構造)
                features = [{"type": "text_extraction"}]
                
                if options:
                    if options.get("detect_tables", True):
                        features.append({"type": "table_detection"})
                    if options.get("detect_formulas", False):
                        features.append({"type": "formula_recognition"})
                
                # 修正されたリクエストデータ構造
                data = {
                    "source": {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_data}"
                        }
                    },
                    "features": features,
                    "language_hint": options.get("language", "ja") if options else "ja"
                }
                
                if options and options.get("detailed_analysis", True):
                    data["options"] = {"detailed_analysis": True}
                
                # OCRクライアントを非同期で実行
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,
                    partial(
                        self.client.ocr.process,
                        model="mistral-ocr-latest",
                        document={
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        },
                        pages=options.get("pages") if options else None,
                        include_image_base64=options.get("include_image_base64", False) if options else False,
                        image_limit=options.get("image_limit") if options else None,
                        image_min_size=options.get("image_min_size") if options else None
                    )
                )
                
                # レスポンスを変換
                if isinstance(response, str):
                    result = {"text": response}
                else:
                    result = response.json() if hasattr(response, 'json') else response

                results.append({
                    "success": True,
                    "text": result["text"] if isinstance(result, dict) else result,
                    "tables": result.get("tables", []) if isinstance(result, dict) else [],
                    "formulas": result.get("formulas", []) if isinstance(result, dict) else []
                })
            
            # 結果を統合
            combined_result = self._combine_page_results(results)
            return combined_result
            
        except Exception as e:
            logger.error(f"Error occurred during PDF processing: {str(e)}")
            return {
                "success": False,
                "error": f"PDF変換/処理エラー: {str(e)}"
            }
    
    def _combine_page_results(self, page_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        複数ページの結果を統合
        
        Args:
            page_results: 各ページの分析結果リスト
            
        Returns:
            統合された結果
        """
        # エラーがあるかチェック
        errors = [r.get("error") for r in page_results if r.get("error")]
        if errors:
            return {
                "success": False,
                "error": f"処理中にエラー発生: {'; '.join(errors[:3])}{'...' if len(errors) > 3 else ''}",
                "page_results": page_results  # 個別結果も保持
            }
        
        # テキスト内容を統合
        combined_text = ""
        combined_tables = []
        combined_formulas = []
        
        for i, result in enumerate(page_results):
            # 成功した結果のみを統合
            if result.get("success", False) is not False:
                page_num = i + 1
                
                # テキスト抽出
                if "text" in result:
                    combined_text += f"\n--- Page {page_num} ---\n{result['text']}\n"
                
                # テーブル抽出
                if "tables" in result:
                    for table in result.get("tables", []):
                        table["page_number"] = page_num
                        combined_tables.append(table)
                
                # 数式抽出
                if "formulas" in result:
                    for formula in result.get("formulas", []):
                        formula["page_number"] = page_num
                        combined_formulas.append(formula)  # 修正: formulaをリストに追加
        
        # 統合結果を作成
        combined_result = {
            "success": True,
            "text": combined_text,
            "page_count": len(page_results)
        }
        
        if combined_tables:
            combined_result["tables"] = combined_tables
        
        if combined_formulas:
            combined_result["formulas"] = combined_formulas
            
        return combined_result

    async def analyze_image(
        self, image_path: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        画像を分析
        
        Args:
            image_path: 分析する画像ファイルのパス
            options: 分析オプション
            
        Returns:
            分析結果
        """
        try:
            # 画像を読み込み
            with open(image_path, "rb") as f:
                image_data = f.read()
            
            # 画像フォーマットの自動検出
            image_ext = Path(image_path).suffix.lower().lstrip('.')
            mime_type = self.SUPPORTED_TYPES.get(image_ext, 'image/jpeg')
            
            # OCRクライアントを非同期で実行
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                partial(
                    self.client.ocr.process,
                    model="mistral-ocr-latest",
                    document={
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:{mime_type};base64,{base64.b64encode(image_data).decode()}"
                        }
                    },
                    pages=options.get("pages") if options else None,
                    include_image_base64=options.get("include_image_base64", False) if options else False,
                    image_limit=options.get("image_limit") if options else None,
                    image_min_size=options.get("image_min_size") if options else None
                )
            )
            
            # レスポンスを変換
            if isinstance(response, str):
                result = {"text": response}
            else:
                result = response.json() if hasattr(response, 'json') else response

            return {
                "success": True,
                "text": result["text"] if isinstance(result, dict) else result,
                "tables": result.get("tables", []) if isinstance(result, dict) else [],
                "formulas": result.get("formulas", []) if isinstance(result, dict) else []
            }
            
        except Exception as e:
                logger.error(f"Error occurred during image analysis: {str(e)}")
                return {
                    "success": False,
                    "error": str(e)
                }

    async def analyze_text(
        self, text: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        テキストを分析
        
        Args:
            text: 分析するテキスト
            options: 分析オプション
            
        Returns:
            分析結果
        """
        try:
            # Prompt configuration (analysis instructions based on options)
            system_prompt = "You are an expert in text analysis."
            user_prompt = "Please analyze the following text"
            
            if options:
                if options.get("extract_economic_concepts", False):
                    system_prompt += " You are particularly knowledgeable about economic concepts and technical terms."
                    user_prompt += ". If there are any economic concepts or technical terms, please explain them in detail"
                if options.get("detect_question_patterns", False):
                    user_prompt += ". Identify any question patterns if present"
                if options.get("language", "ja") != "ja":
                    system_prompt = "You are a text analysis expert."
                    user_prompt = "Please analyze the following text"
            
            # 新しいChat APIを使用
            response = await self.client.chat(
                model="mistral-medium",
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": f"{user_prompt}:\n\n{text}"
                    }
                ]
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content
            }
            
        except Exception as e:
            logger.error(f"Error occurred during text analysis: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


    async def batch_analyze(
        self, files: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        複数のファイルを一括で分析
        
        Args:
            files: ファイルパスとオプションの辞書
                  {"file_path": {"type": "document|image|text", "options": {...}}}
            
        Returns:
            ファイルパスと分析結果の辞書
        """
        tasks = []
        for file_path, config in files.items():
            file_type = config.get("type", "document")
            options = config.get("options", {})
            
            if file_type == "document":
                task = self.analyze_document(file_path, options)
            elif file_type == "image":
                task = self.analyze_image(file_path, options)
            elif file_type == "text":
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        text = f.read()
                    task = self.analyze_text(text, options)
                except UnicodeDecodeError:
                    # UTF-8でデコードできない場合は代替エンコーディングを試行
                    with open(file_path, 'r', encoding='shift-jis') as f:
                        text = f.read()
                    task = self.analyze_text(text, options)
            else:
                logger.error(f"Unsupported file type: {file_type}")
                continue
            
            tasks.append((file_path, task))
        
        results = {}
        for file_path, task in tasks:
            try:
                result = await task
                results[file_path] = result
            except Exception as e:
                logger.error(f"Error processing {file_path}: {str(e)}")
                results
