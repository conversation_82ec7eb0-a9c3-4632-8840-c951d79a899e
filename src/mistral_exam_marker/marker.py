"""
Core module of Mistral Exam Marker system integrating Mistral OCR and Gemini API
"""

import os
import json
import time
import asyncio
import logging
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
import fitz  # PyMuPDF
from PIL import Image
import io
import google.generativeai as genai
from google.generativeai import types
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import base64

from .document_processor import DocumentProcessor
from .markdown_converter import MarkdownConverter

# ロガーの設定
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

class MistralExamMarker:
    def __init__(self, mistral_api_key: str, gemini_api_key: str, marker_name: str):
        """Initialize MistralExamMarker with API keys and marker name."""
        self.working_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.document_processor = DocumentProcessor(mistral_api_key=mistral_api_key)

        # Gemini API設定
        genai.configure(api_key=gemini_api_key)
        self.client = genai
        self.model = self.client.GenerativeModel('gemini-2.5-flash-preview-05-20')
        self.generation_config = types.GenerationConfig(
            temperature=0.1,
            top_p=0.95,
            top_k=40,
            max_output_tokens=8192,
        )

        self.marker_name = marker_name
        self.questions = {}
        self.solutions = {}
        self.criteria = {}
        self.guidelines = []
        self._load_criteria()

        self.statistics = {
            'total_exams': 0,
            'total_pages': 0,
            'processing_time': 0,
            'api_calls': 0,
            'ocr_calls': 0
        }

        # 既に割り当てられたグラフのパスを追跡
        self.assigned_graphs = set()

        self.last_api_call = 0
        self.MIN_API_INTERVAL = 30.0  # APIリクエストの最小間隔を30秒に増加
        self.api_semaphore = asyncio.Semaphore(1)
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.markdown_converter = MarkdownConverter()

        # 出力ディレクトリ
        self.output_dir = Path("outputs/mistral_exams")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 一時ディレクトリ
        self.temp_dir = Path("temp_mistral_ocr")
        self.temp_dir.mkdir(exist_ok=True)

        self.all_feedback_contents: List[str] = []

    def set_output_directory(self, output_dir: str) -> None:
        """
        出力ディレクトリを設定する

        Args:
            output_dir: 出力ディレクトリのパス
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Output directory set: {self.output_dir}")

    def get_output_directory(self) -> str:
        """
        現在の出力ディレクトリを取得する

        Returns:
            出力ディレクトリのパス
        """
        return str(self.output_dir)

    def _load_criteria(self):
        """Load marking criteria from environment variables."""
        for key, value in os.environ.items():
            if key.startswith('MARKING_GUIDELINE_'):
                self.guidelines.append(value)
            elif key.startswith('QUESTION_TEXT_'):
                q_id = key.replace('QUESTION_TEXT_', '')
                self.questions[q_id] = value
            elif key.startswith('SOLUTION_'):
                q_id = key.replace('SOLUTION_', '')
                self.solutions[q_id] = value
            elif key.startswith('QUESTION_') and not key.startswith('QUESTION_TEXT_'):
                q_id = key.replace('QUESTION_', '')
                criteria_list = []
                for criterion in value.split('|'):
                    marks, desc = criterion.split(',', 1)
                    criteria_list.append({
                        'marks': int(marks),
                        'description': desc
                    })
                self.criteria[q_id] = criteria_list

    def _extract_answer_from_text(self, full_text: str, q_id: str) -> str:
        """
        Extract answer text for a specific question from full text.

        Args:
            full_text: The full text to search in
            q_id: Question ID (e.g., "1a", "2b")

        Returns:
            Extracted answer text or empty string if not found
        """
        # 質問IDが不明な場合は空文字を返す
        if not q_id or q_id.startswith("unknown_"):
            return ""

        # 数字部分とアルファベット部分を分離
        if len(q_id) >= 2 and q_id[0].isdigit() and q_id[-1].isalpha():
            base_q_num = q_id[:-1]  # e.g., "1" from "1a"
            sub_q_char = q_id[-1]   # e.g., "a" from "1a"
        else:
            # 不正な形式の場合は空文字を返す
            return ""

        # 標準的なパターン
        patterns = [
            # 1a) や 1a: のような形式
            rf'{re.escape(q_id)}\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+[a-zA-Z]\s*[:.]|$)',
            # 1 a) や 1 A) のような形式
            rf'{base_q_num}\s+{sub_q_char}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[a-zA-Z]\)\s*[:.]|$)',
            # a) や A) のような形式
            rf'{sub_q_char}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\s*[:.]|$)',
            # a: や A: のような形式
            rf'{sub_q_char}\s*[:]\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\s*[:.]|$)',
            # 2 A) や 2 B) のような形式
            rf'{base_q_num}\s+{sub_q_char.upper()}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[a-zA-Z]\)\s*[:.]|$)',
            # 2 A. や 2 B. のような形式
            rf'{base_q_num}\s+{sub_q_char.upper()}\.\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[a-zA-Z]\.\s*[:.]|$)',
            # 2 A: や 2 B: のような形式
            rf'{base_q_num}\s+{sub_q_char.upper()}\:\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[a-zA-Z]\:\s*[:.]|$)'
        ]

        # 単独のアルファベットのパターン（問題番号なし）
        standalone_patterns = [
            # a) や A) のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\)\s*[:.]|$)',
            # a. や A. のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char}\.\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\.\s*[:.]|$)',
            # a: や A: のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char}\:\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\:\s*[:.]|$)',
            # 大文字のA), B), C)のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char.upper()}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\)\s*[:.]|$)',
            # 大文字のA., B., C.のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char.upper()}\.\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\.\s*[:.]|$)',
            # 大文字のA:, B:, C:のような形式（問題番号なし）
            rf'(?:(?<!\d)|(?<!\d\s)){sub_q_char.upper()}\:\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\:\s*[:.]|$)'
        ]

        # すべてのパターンを試す
        all_patterns = patterns + standalone_patterns
        for pattern in all_patterns:
            match = re.search(pattern, full_text, flags=re.DOTALL | re.IGNORECASE)
            if match and match.group("answer").strip():
                return match.group("answer").strip()

        # 問題文に基づく検索（最終手段）
        if q_id in self.questions:
            question_text = self.questions[q_id]
            # 問題文の特徴的な部分を抽出（最初の数単語）
            words = question_text.split()
            if len(words) >= 3:
                search_terms = ' '.join(words[:3])
                # 問題文の特徴的な部分を含む段落を探す
                paragraphs = full_text.split('\n\n')
                for i, para in enumerate(paragraphs):
                    if search_terms.lower() in para.lower() and i + 1 < len(paragraphs):
                        # 次の段落が回答の可能性が高い
                        return paragraphs[i + 1].strip()

        # 問題番号のみ + 単独のアルファベットのパターン（例：「2」の後に「b」や「c」が続く場合）
        if len(q_id) >= 2 and q_id[0].isdigit() and q_id[-1].isalpha():
            base_q_num = q_id[:-1]  # e.g., "1" from "1a"
            sub_q_char = q_id[-1]   # e.g., "a" from "1a"

            # 問題番号のみのパターン
            base_patterns = [
                rf'{base_q_num}\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\s*[:.]|$)',  # 2: または 2.
                rf'{base_q_num}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*\d+\)\s*[:.]|$)'  # 2) または 2):
            ]

            # まず問題番号のみのパターンを探す
            base_match = None
            base_answer = ""

            for pattern in base_patterns:
                match = re.search(pattern, full_text, flags=re.DOTALL | re.IGNORECASE)
                if match and match.group("answer").strip():
                    base_match = match
                    base_answer = match.group("answer").strip()
                    break

            # 問題番号が見つかった場合、その中から小問を探す
            if base_match and base_answer:
                # 小問のパターン
                sub_patterns = [
                    rf'{sub_q_char}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\)\s*[:.]|$)',  # b) または b):
                    rf'{sub_q_char}\.\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\.\s*[:.]|$)',  # b. または b.:
                    rf'{sub_q_char}\s*[:]\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\s*[:.]|$)',        # b: または b :
                    rf'{sub_q_char.upper()}\)\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\)\s*[:.]|$)',  # B) または B):
                    rf'{sub_q_char.upper()}\.\s*[:.]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\.\s*[:.]|$)',  # B. または B.:
                    rf'{sub_q_char.upper()}\s*[:]\s*(?P<answer>.*?)(?=\n\s*[A-Z]\s*[:.]|$)'         # B: または B :
                ]

                # 小問のパターンを探す
                for pattern in sub_patterns:
                    match = re.search(pattern, base_answer, flags=re.DOTALL | re.IGNORECASE)
                    if match and match.group("answer").strip():
                        return match.group("answer").strip()

        return ""

    def _calculate_graph_relevance(self, ocr_data: Dict[str, Any], q_id: str) -> float:
        """
        Calculate relevance score for a graph in relation to a question.

        Args:
            ocr_data: OCR data for the image
            q_id: Question ID

        Returns:
            Relevance score between 0 and 1
        """
        score = 0.0

        # OCRテキストを取得
        text = str(ocr_data.get("raw_text", "")).lower()

        # 基本スコア: グラフ要素の存在
        if ocr_data.get("content_type") == "graph":
            score += 0.3

        # 軸の存在でスコア加算
        if ocr_data.get("axes"):
            score += 0.2

        # グラフ種別の存在でスコア加算
        if ocr_data.get("graph_type"):
            score += 0.2

        # 質問に関連するキーワードの存在でスコア加算
        if q_id.startswith('1'):  # 問題1に関連する経済用語
            keywords = ['supply', 'demand', 'equilibrium', 'market', 'price', 'quantity']
        else:  # 問題2に関連する経済用語
            keywords = ['gdp', 'growth', 'output', 'income', 'consumption', 'investment']

        # キーワードごとのスコア加算(最大0.3)
        keyword_score = 0
        for keyword in keywords:
            if keyword in text:
                keyword_score += 0.05
        score += min(keyword_score, 0.3)

        return min(score, 1.0)

    def _find_relevant_graph(self, ocr_result: Dict[str, Any], q_id: str) -> Optional[Dict[str, Any]]:
        """Find graphs relevant to a specific question from OCR results."""
        requires_graph = any(
            keyword in criterion['description'].lower()
            for criterion in self.criteria.get(q_id, [])
            for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']
        )

        if not requires_graph:
            return None

        structured_data = ocr_result.get("structured_data", {})
        question_page = None

        # すべてのグラフ必要な設問を収集
        graph_requiring_questions = []
        for question_id in self.questions.keys():
            if any(
                keyword in criterion['description'].lower()
                for criterion in self.criteria.get(question_id, [])
                for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']
            ):
                graph_requiring_questions.append(question_id)

        # 質問のページ番号を特定
        for qa in structured_data.get("questions_and_answers", []):
            if qa.get("question_id") == q_id:
                question_page = qa.get("page_num")
                break

        # 質問ページが見つからない場合は他の方法で探す
        if question_page is None:
            question_page = self._find_question_page(ocr_result, q_id)

        # デバッグ情報を追加
        logger.info(f"Looking for graph for question {q_id} (page: {question_page})")

        # 画像候補とスコアの初期化
        all_images = []
        graph_candidates = []
        best_score = 0
        best_graph = None
        nearest_distance = float('inf')
        nearest_graph = None

        # 抽出された画像の総数を記録
        total_images = len(ocr_result.get("embedded_images", []))
        logger.info(f"Total embedded images: {total_images}")

        for img_info in ocr_result.get("embedded_images", []):
            page_distance = abs(img_info.get("page_num", 0) - (question_page or 0))
            img_path = img_info.get("path", "")
            all_images.append(img_info)  # すべての画像を保存

            # グラフでなくても画像情報は記録
            logger.debug(f"Image on page {img_info.get('page_num')}: {img_path}")

            # 最も近い画像を記録（絶対的なフォールバック用）
            if page_distance < nearest_distance:
                nearest_distance = page_distance
                nearest_graph = img_info

            # OCRデータの詳細な分析とログ
            ocr_data = img_info.get("ocr_data", {})
            logger.info(f"\nAnalyzing OCR data for image at {img_path}:")
            logger.info(f"  Content type: {ocr_data.get('content_type', 'Not specified')}")
            logger.info(f"  Raw text length: {len(str(ocr_data.get('raw_text', '')))}")

            # 画像タイプを判定 (より柔軟に)
            is_graph = False
            graph_type = ocr_data.get("content_type")

            # グラフ判定の詳細なログ
            logger.info("Graph detection analysis:")
            if graph_type == "graph":
                is_graph = True
                logger.info("  - Directly identified as graph by content_type")
            elif ocr_data.get("graph_type") or ocr_data.get("axes"):
                is_graph = True
                logger.info(f"  - Identified as graph from metadata:")
                if ocr_data.get("graph_type"):
                    logger.info(f"    * Graph type: {ocr_data.get('graph_type')}")
                if ocr_data.get("axes"):
                    logger.info(f"    * Axes info: {ocr_data.get('axes')}")

            # テキストベースの分析
            text_content = str(ocr_data.get("raw_text", "")).lower()
            graph_keywords = ['axis', 'curve', 'supply', 'demand', 'graph']
            found_keywords = [kw for kw in graph_keywords if kw in text_content]
            if found_keywords:
                is_graph = True
                logger.info("  - Identified as graph from text content:")
                logger.info(f"    * Found keywords: {', '.join(found_keywords)}")

            # 各設問への関連性スコアを計算
            scores = {}
            adjusted_score = 0  # 初期化
            for question_id in graph_requiring_questions:
                curr_page = None
                for qa in structured_data.get("questions_and_answers", []):
                    if qa.get("question_id") == question_id:
                        curr_page = qa.get("page_num")
                        break

                if curr_page is None:
                    curr_page = self._find_question_page(ocr_result, question_id)

                page_distance = abs(img_info.get("page_num", 0) - (curr_page or 0))

                # 基本スコアを計算
                base_score = self._calculate_graph_relevance(ocr_data, question_id)

                # ページ距離ボーナスを計算
                distance_bonus = 0
                if page_distance == 0:
                    distance_bonus = 1.0  # 同じページにある場合のボーナスを大きくする
                elif page_distance == 1:
                    distance_bonus = 0.3
                elif page_distance == 2:
                    distance_bonus = 0.1

                base_score = self._calculate_graph_relevance(ocr_data, question_id)
                if img_path in self.assigned_graphs:
                    # 既に割り当てられているグラフのスコアを大幅に下げる
                    base_score = base_score * 0.3  # 70%減
                    distance_bonus = distance_bonus * 0.3
                    logger.info(f"  Score reduction for {question_id}: 70% (already assigned)")

                final_score = base_score + distance_bonus
                adjusted_score = final_score if is_graph else final_score * 0.5

                scores[question_id] = {
                    "score": adjusted_score,
                    "page_distance": page_distance,
                    "is_assigned": img_path in self.assigned_graphs
                }

                if question_id == q_id:
                    current_score = adjusted_score

            # 関連性スコアをログに記録
            logger.info("\nRelevance scores for all questions:")
            for question_id, score_info in scores.items():
                logger.info(f"  Question {question_id}: {score_info['score']:.2f} (distance: {score_info['page_distance']})")

            if q_id in scores and scores[q_id]["score"] > best_score:
                best_score = scores[q_id]["score"]
                best_graph = {
                    "success": True,
                    "graph_type": ocr_data.get("graph_type", "Economic Graph"),
                    "axes": ocr_data.get("axes", {"x": "X-axis", "y": "Y-axis"}),
                    "trends": ocr_data.get("trends", []),
                    "points_of_interest": ocr_data.get("key_points", []),
                    "economic_interpretation": ocr_data.get("economic_interpretation", ""),
                    "raw_response": ocr_data.get("raw_text", ""),
                    "image_path": img_path,
                    "scores": scores  # 全設問のスコアを保持
                }

            if is_graph and adjusted_score > best_score:
                best_score = adjusted_score
                best_graph = {
                    "success": True,
                    "graph_type": ocr_data.get("graph_type", "Economic Graph"),
                    "axes": ocr_data.get("axes", {"x": "X-axis", "y": "Y-axis"}),
                    "trends": ocr_data.get("trends", []),
                    "points_of_interest": ocr_data.get("key_points", []),
                    "economic_interpretation": ocr_data.get("economic_interpretation", ""),
                    "raw_response": ocr_data.get("raw_text", ""),
                    "image_path": img_info.get("path", ""),
                }

        # グラフ候補の中から最適なものを選択
        if best_graph:
            # 他の設問に対するスコアも確認
            all_scores = best_graph.pop("scores", {})

            # 各設問のスコアをログに記録
            logger.info("\nFinal scores for all questions:")
            for qid, score_info in all_scores.items():
                logger.info(f"  Question {qid}: {score_info['score']:.2f} "
                        f"(distance: {score_info['page_distance']}, "
                        f"{'already assigned' if score_info.get('is_assigned') else 'available'})")

            # スコアと割り当て状態を考慮して最適な設問を選択
            def get_effective_score(score_info):
                score = score_info["score"]
                if score_info["is_assigned"]:
                    # 割り当て済みの場合はスコアを大幅に下げる
                    score *= 0.3
                return score

            # 各設問の実効スコアを計算
            effective_scores = {
                qid: get_effective_score(score_info)
                for qid, score_info in all_scores.items()
            }

            # effective_scoresが空でないことを確認
            if not effective_scores:
                logger.warning(f"No effective scores calculated for question {q_id}")
                return None

            # 最高スコアの設問を見つける
            max_score_question = max(effective_scores.items(), key=lambda x: x[1])[0]

            # 他の設問のスコアを取得（現在の設問以外）
            other_scores = [
                score for qid, score in effective_scores.items()
                if qid != q_id
            ]

            # 現在の設問のスコアが最大、または他の設問により高いスコアがない場合に割り当て
            current_score = effective_scores.get(q_id, 0)
            if max_score_question == q_id or (
                not other_scores or  # 他の設問がない場合
                current_score >= max(other_scores) * 0.95  # 95%以上のスコアなら許容
            ):
                logger.info(f"Found best graph for question {q_id} with score {best_score:.2f}")
                self.assigned_graphs.add(best_graph["image_path"])
                return best_graph
            else:
                max_score_value = effective_scores.get(max_score_question, 0)
                logger.info(f"Skipping - Graph has higher relevance for question {max_score_question} "
                        f"(effective score: {max_score_value:.2f} vs {current_score:.2f})")
                return None

        # すべてのグラフをスコア順にソート
        all_graphs = []
        for img in all_images:
            if img.get("content_type") == "graph" or any(
                keyword in str(img.get("raw_text", "")).lower()
                for keyword in ["axis", "curve", "supply", "demand", "graph"]
            ):
                img_path = img.get("path")
                page_distance = abs(img.get("page_num", 0) - (question_page or 0))

                # グラフのスコアを計算
                base_score = self._calculate_graph_relevance(img.get("ocr_data", {}), q_id)
                distance_bonus = 0.8 if page_distance == 0 else 0.3 if page_distance == 1 else 0.1
                final_score = base_score + distance_bonus

                all_graphs.append({
                    "img_info": img,
                    "score": final_score,
                    "page_distance": page_distance,
                    "is_assigned": img_path in self.assigned_graphs
                })

        # スコア順にソート（未割り当てを優先）
        all_graphs.sort(key=lambda x: (
            -x["score"],  # スコアの高い順
            x["page_distance"],  # ページ距離が近い順
            x["is_assigned"]  # 未割り当てを優先
        ))

        # 未割り当てのグラフを優先的に使用
        for graph in all_graphs:
            if not graph["is_assigned"]:
                img_info = graph["img_info"]
                img_path = img_info.get("path")
                ocr_data = img_info.get("ocr_data", {})
                logger.info(f"Using available graph for question {q_id} (score: {graph['score']:.2f})")
                self.assigned_graphs.add(img_path)
                return {
                    "success": True,
                    "graph_type": ocr_data.get("graph_type", "Economic Graph"),
                    "axes": ocr_data.get("axes", {"x": "X-axis", "y": "Y-axis"}),
                    "trends": ocr_data.get("trends", []),
                    "points_of_interest": ocr_data.get("key_points", []),
                    "economic_interpretation": "Graph assigned based on availability.",
                    "raw_response": ocr_data.get("raw_text", ""),
                    "image_path": img_path
                }

        # すべてのグラフが割り当て済みの場合は、最もスコアの高いものを再利用
        if all_graphs:
            best_graph = all_graphs[0]
            img_info = best_graph["img_info"]
            img_path = img_info.get("path")
            ocr_data = img_info.get("ocr_data", {})
            logger.info(f"Reusing assigned graph for question {q_id} (score: {best_graph['score']:.2f})")

            return {
                "success": True,
                "graph_type": ocr_data.get("graph_type", "Economic Graph"),
                "axes": ocr_data.get("axes", {"x": "X-axis", "y": "Y-axis"}),
                "trends": ocr_data.get("trends", []),
                "points_of_interest": ocr_data.get("key_points", []),
                "economic_interpretation": "Graph reused due to all graphs being allocated.",
                "raw_response": ocr_data.get("raw_text", ""),
                "image_path": img_path
            }

        logger.warning(f"No graph found for question {q_id} despite all attempts")
        return None

    async def evaluate_answer(self, question_id: str, answer_text: str,
                            graph_analysis: Optional[Dict] = None) -> Tuple[int, str]:
        """Evaluate student's answer using Gemini API with graph analysis integration."""
        async with self.api_semaphore:
            await self._wait_for_rate_limit()

            criteria = self.criteria[question_id]
            total_marks = sum(c['marks'] for c in criteria)

            # Initialize graph-related variables
            requires_graph = any(
                keyword in criterion['description'].lower()
                for criterion in criteria
                for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve', 'chart']
            )

            # Check if graph is required but not provided
            graph_missing = requires_graph and (not graph_analysis or not graph_analysis.get('success'))

            # 画像のパスを取得とバリデーション
            graph_image_path = None
            try:
                if graph_analysis and isinstance(graph_analysis, dict):
                    if graph_analysis.get('success'):
                        graph_image_path = graph_analysis.get('image_path')
                        if graph_image_path and isinstance(graph_image_path, str):
                            if os.path.exists(graph_image_path) and os.path.isfile(graph_image_path):
                                logger.info(f"Graph image path validated: {graph_image_path}")
                            else:
                                logger.warning(f"Invalid graph image path: {graph_image_path}")
                                graph_image_path = None
                        else:
                            logger.warning("Invalid or missing image path in graph analysis")
                            graph_image_path = None
                    else:
                        logger.warning("Graph analysis unsuccessful")
                else:
                    logger.warning("Invalid graph analysis data")
            except Exception as e:
                logger.error(f"Error processing graph analysis: {str(e)}")
                graph_image_path = None

        graph_analysis_text = ""
        graph_feedback = []

        # Process graph analysis if required
        if requires_graph:
            if graph_analysis and graph_analysis.get('success'):
                # Generate graph analysis text to include in prompt
                try:
                    axes = graph_analysis.get('axes', {})
                    if not isinstance(axes, dict):
                        axes = {}
                        logger.warning("Invalid axes format in graph analysis")

                    graph_analysis_text = f"""
Graph/Chart Analysis:
1. Type: {str(graph_analysis.get('graph_type', 'Economic Graph'))}
2. Components:
   - X-axis: {str(axes.get('x', 'X-axis'))}
   - Y-axis: {str(axes.get('y', 'Y-axis'))}
3. Key Features:
   - Trends: {', '.join(str(t) for t in (graph_analysis.get('trends', []) or ['Not specified']) if t)}
   - Points of Interest: {', '.join(str(p) for p in (graph_analysis.get('points_of_interest', []) or ['Not specified']) if p)}
4. Economic Interpretation:
{str(graph_analysis.get('economic_interpretation')) if graph_analysis.get('economic_interpretation') else 'Basic economic relationships are illustrated.'}
"""
                    logger.debug("Graph analysis text generated successfully")
                except Exception as e:
                    logger.error(f"Error in graph analysis text generation: {str(e)}")
                    graph_analysis_text = "\nGraph Analysis: Unable to generate analysis text due to data format issues.\n"
                try:
                    # グラフの品質評価を初期化
                    graph_qualities = []

                    # グラフの基本要素をチェック
                    if graph_analysis and isinstance(graph_analysis, dict):
                        # グラフタイプの確認
                        if graph_type := graph_analysis.get('graph_type'):
                            if isinstance(graph_type, str):
                                # 軸情報の確認
                                if axes and isinstance(axes, dict):
                                    x_axis = str(axes.get('x', '')) if axes else ''
                                    y_axis = str(axes.get('y', '')) if axes else ''
                                    if x_axis and y_axis:
                                        graph_qualities.append("- Graph present and complete")
                                        graph_qualities.append("- Proper axis labels displayed")
                                        logger.info("Graph axes validation successful")
                                    else:
                                        logger.warning("Invalid or missing axis labels")
                                else:
                                    logger.warning("Invalid axes data structure")
                            else:
                                logger.warning("Invalid graph type format")
                        else:
                            logger.warning("Missing graph type")

                        # トレンドと重要ポイントの確認
                        trends = graph_analysis.get('trends', []) if graph_analysis else []
                        key_points = graph_analysis.get('points_of_interest', []) if graph_analysis else []

                        if isinstance(trends, list) and isinstance(key_points, list):
                            if any(trends) and any(key_points):
                                graph_qualities.append("- Clear trends and key points identified")
                                logger.info("Trends and key points validation successful")
                            else:
                                logger.warning("Empty trends or key points")
                        else:
                            logger.warning("Invalid trends or key points format")

                        # 経済的解釈の確認
                        if interpretation := graph_analysis.get('economic_interpretation'):
                            if isinstance(interpretation, str):
                                if len(interpretation.split()) > 50:
                                    graph_qualities.append("- In-depth economic interpretation provided")
                                    logger.info("Economic interpretation validation successful")
                                else:
                                    logger.warning("Economic interpretation too brief")
                            else:
                                logger.warning("Invalid economic interpretation format")
                        else:
                            logger.warning("Missing economic interpretation")
                    else:
                        logger.warning("Invalid graph analysis structure")

                except Exception as e:
                    logger.error(f"Error evaluating graph quality: {str(e)}")
                    graph_qualities = []

                if graph_qualities:
                    graph_analysis_text += "5. Graph Quality Assessment:\n"
                    graph_analysis_text += "\n".join(graph_qualities)

                if graph_image_path:
                    graph_analysis_text += "\n\nNOTE: I am also providing the actual student graph as an image for you to analyze directly."
            else:
                graph_analysis_text = "\nGraph Analysis: Required graph or diagram not found in the student's answer. Any criteria requiring a graph must receive zero points.\n"

        # Store evaluation results for each criterion
        criteria_evaluations = []
        total_score = 0

        # Evaluate each criterion individually
        # 全ての採点基準をプロンプトに含める
        criteria_list = self.criteria.get(question_id, [])
        criteria_text = "\n".join(f"- {c['marks']} points: {c['description']}" for c in criteria_list)
        for criterion in criteria:
            # チェックしてグラフが必要かどうか判定
            is_graph_criterion = any(
                keyword in criterion['description'].lower()
                for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve', 'chart']
            )

            # Create prompt for individual criterion
            prompt = f"""Evaluate the student's answer based on the following marking criteria:

All Criteria for Question {question_id}:
{criteria_text}

Specific Criterion:
- Marks: {criterion['marks']} points
- Description: {criterion['description']}

Question {question_id}:
{self.questions[question_id]}

Model Answer:
{self.solutions[question_id]}

Marking Guidelines:
{chr(10).join('- ' + g for g in self.guidelines)}

Student's Answer:
{answer_text}

{graph_analysis_text}

"""

            # グラフが必要なのに存在しない場合、特別な指示を追加
            if is_graph_criterion and graph_missing:
                prompt += """
IMPORTANT GRADING INSTRUCTION: This criterion explicitly requires a graph/diagram, but the student did not provide one.
YOU MUST AWARD ZERO (0) POINTS for this criterion, regardless of the quality of the text explanation.
In your feedback, clearly state that zero points are awarded because no graph was provided.
"""

            if is_graph_criterion and graph_image_path and os.path.exists(graph_image_path):
                prompt += """
IMPORTANT: You are being provided with the student's actual graph as an image. Please carefully analyze this image directly
and use it as the primary source for evaluating the graphical portion of the answer. The OCR text analysis may not perfectly
capture all elements of the hand-drawn graph, so your visual analysis of the image is critical.
"""

            prompt += f"""
Return only a JSON object in the following format:
{{
    "marks": <integer between 0 and {criterion['marks']}>,
    "feedback": "<detailed feedback for this criterion>",
    "analysis": {{
        "key_points": "<identified key economic concepts>",
        "strengths": "<strengths of the answer>",
        "weaknesses": "<areas needing improvement>"
    }}
}}"""

            try:
                # リクエストの内容をログ出力
                logger.info(f"Gemini API Request for Question {question_id}:")
                logger.info(f"Criterion: {criterion['description']}")
                logger.debug(f"Full prompt content: {prompt}")

                # 画像を含むAPIコールを行う
                if is_graph_criterion and graph_image_path and os.path.exists(graph_image_path):
                    logger.info(f"Evaluating criterion with graph image: {graph_image_path}")
                    response = await self._generate_content(prompt, graph_image_path)
                else:
                    response = await self._generate_content(prompt)

                self.statistics['api_calls'] += 1

                # レスポンスの内容をログ出力
                logger.info(f"Gemini API Response for Question {question_id}:")
                logger.debug(f"Raw response: {response.text if hasattr(response, 'text') else str(response)}")

                # レスポンスの処理
                try:
                    try:
                        # レスポンスの正規化
                        response_text = response.text if hasattr(response, 'text') else str(response)
                        response_text = response_text.strip()

                        logger.info(f"Processing response for Question {question_id}, Criterion: {criterion['description']}")
                        logger.debug("Normalized response text:")
                        logger.debug(response_text)

                        # JSONフォーマットの検出と抽出
                        json_text = response_text
                        if '```' in response_text:
                            match = re.search(r'```(?:json)?\s*({[\s\S]*?})\s*```', response_text)
                            if match:
                                json_text = match.group(1).strip()
                                logger.debug("Extracted JSON from code block:")
                                logger.debug(json_text)
                            else:
                                logger.warning("JSON code block detected but format is invalid")

                        # JSONパースの試行
                        try:
                            result = json.loads(json_text)
                            logger.debug("Successfully parsed JSON response:")
                            logger.debug(str(result))
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse JSON: {str(e)}")
                            logger.debug(f"Attempted to parse: {json_text}")
                            raise ValueError(f"Invalid JSON format: {str(e)}")

                    except Exception as e:
                        logger.error(f"Response processing error: {str(e)}")
                        raise ValueError(f"Failed to process response: {str(e)}")

                    # 必須フィールドの検証
                    if not isinstance(result.get('marks'), (int, float)):
                        raise ValueError("Invalid marks value in response")

                    # グラフが必要なのに存在しない場合は強制的に0点にする
                    try:
                        if is_graph_criterion and graph_missing:
                            marks = 0
                            feedback = "Zero points awarded because no graph/diagram was provided as required. " + result.get('feedback', "")
                        else:
                            marks = min(max(0, int(float(str(result.get('marks', 0))))), criterion['marks'])
                            feedback = result.get('feedback', "")
                    except (TypeError, ValueError) as e:
                        logger.error(f"Error processing marks: {str(e)}")
                        marks = 0
                        feedback = "Error processing marks"

                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.error(f"Error processing response: {str(e)}")
                    marks = 0
                    feedback = f"Error evaluating answer: {str(e)}"

                criteria_evaluations.append({
                    'criterion': criterion['description'],
                    'marks': marks,
                    'max_marks': criterion['marks'],
                    'feedback': feedback
                })

                total_score += marks

            except Exception as e:
                logger.error(f"Error evaluating criterion '{criterion['description']}': {str(e)}")
                criteria_evaluations.append({
                    'criterion': criterion['description'],
                    'marks': 0,
                    'max_marks': criterion['marks'],
                    'feedback': f"Evaluation error: {str(e)}"
                })

        # Create graph score summary only for display purposes
        graph_score_text = ""
        if requires_graph:
            graph_criteria = [c for c in criteria if any(
                keyword in c['description'].lower()
                for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve', 'chart']
            )]
            graph_marks_awarded = sum(e['marks'] for e in criteria_evaluations
                                     if any(keyword in e['criterion'].lower()
                                           for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve', 'chart']))
            graph_marks_available = sum(c['marks'] for c in graph_criteria)

            if graph_missing:
                graph_score_text = f"\nGraph Score: 0/{graph_marks_available} points (No graph/diagram provided)\n"
            else:
                graph_score_text = f"\nGraph Score: {graph_marks_awarded}/{graph_marks_available} points\n"

        # Display grading results
        feedback_sections = [
            f"## Question {question_id} Grading Results",
            f"Total Score: {total_score}/{total_marks} points\n",
            f"### Feedback:\n",
            graph_score_text
        ]

        # Display details for each criterion with the score at the end of each feedback comment
        for i, eval in enumerate(criteria_evaluations, 1):
            # Format the score to be appended at the end of the feedback
            points_info = f"({eval['marks']}/{eval['max_marks']} points)"

            # Get the feedback text and ensure it doesn't already end with the score
            feedback_text = eval['feedback'].strip()
            if not feedback_text.endswith(points_info):
                # Add the score at the end of the feedback text
                feedback_text = f"{feedback_text} {points_info}"

            # Add the formatted feedback to the sections
            feedback_sections.append(f"{i}. {feedback_text}\n")

        return total_score, "\n".join(feedback_sections)

    async def process_pdf(self, pdf_path: str) -> str:
        """
        Process PDF and generate grading feedback

        Args:
            pdf_path: Path to PDF file to process

        Returns:
            Markdown formatted feedback document
        """
        start_time = time.time()
        candidate_number = Path(pdf_path).stem

        try:
            logger.info(f"Processing started: {pdf_path} (Candidate Number: {candidate_number})")

            # Step 1: Mistral OCRでPDFを処理
            logger.info("Starting document processing with Mistral OCR...")
            ocr_result = await self.handle_mistral_api_error(self.document_processor.process_pdf, pdf_path)
            self.statistics['ocr_calls'] += 1

            if not ocr_result.get("success", False):
                error_msg = ocr_result.get("error", "Unknown OCR processing error")
                logger.error(f"OCR processing error: {error_msg}")
                return f"Error processing PDF: {error_msg}"

            # 抽出された画像の概要を記録
            embedded_images = ocr_result.get("embedded_images", [])
            logger.info(f"Extracted {len(embedded_images)} images from PDF")
            for i, img in enumerate(embedded_images):
                logger.info(f"Image {i+1}: page {img.get('page_num')}, path: {img.get('path')}")
                ocr_data = img.get("ocr_data", {})
                if ocr_data:
                    logger.info(f"  - Type: {ocr_data.get('content_type')}, Text length: {len(str(ocr_data.get('raw_text', '')))}")

            # 設問ごとに必要なグラフを特定
            graph_requiring_questions = []
            for q_id in self.questions:
                requires_graph = any(
                    keyword in criterion['description'].lower()
                    for criterion in self.criteria.get(q_id, [])
                    for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']
                )
                if requires_graph:
                    graph_requiring_questions.append(q_id)

            logger.info(f"Questions requiring graphs: {graph_requiring_questions}")

            # Step 2: 回答を構造化データとして抽出
            marks_and_feedback = {}
            total_marks = 0
            total_available = 0
            feedback_sections = [
                "# Grading Results",
                f"Candidate Number: {candidate_number}\n"
            ]

            # Step 3: 各質問を評価
            for q_id in sorted(self.questions.keys()):
                logger.info(f"Starting grading for question {q_id}...")

                # 回答テキストを抽出
                answer_text = self._find_answer_from_ocr_results(ocr_result, q_id)

                # グラフ情報を探す
                graph_analysis = self._find_relevant_graph(ocr_result, q_id)

                # グラフが必要かどうかログに記録
                requires_graph = any(
                    keyword in criterion['description'].lower()
                    for criterion in self.criteria.get(q_id, [])
                    for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve', 'chart']
                )

                if requires_graph:
                    if graph_analysis:
                        logger.info(f"Graph found and associated with question {q_id}")
                        graph_path = graph_analysis.get('image_path', '')
                        if graph_path:
                            logger.info(f"Graph image path: {graph_path}")
                    else:
                        logger.warning(f"Question {q_id} requires a graph but none was found")

                try:
                    # Gemini APIで採点
                    marks, feedback = await self.evaluate_answer(q_id, answer_text, graph_analysis)
                    total_marks += marks

                    # Calculate available marks from criteria
                    available_marks = sum(c['marks'] for c in self.criteria.get(q_id, []))
                    total_available += available_marks

                    # 採点結果を保存
                    marks_and_feedback[q_id] = {
                        "marks": marks,
                        "feedback": feedback,
                        "available_marks": available_marks
                    }

                    # フィードバックを作成
                    feedback_text = f"\n## Question {q_id}\n"
                    feedback_text += f"Total Marks: {marks_and_feedback[q_id]['available_marks']} points\n"
                    feedback_text += f"Score: {marks} points\n\n"
                    feedback_text += f"Feedback:\n{feedback}\n"

                    if graph_analysis and graph_analysis.get("success"):
                        feedback_text += "\nGraph Analysis:\n"
                        feedback_text += f"- Type: {graph_analysis['graph_type']}\n"
                        feedback_text += f"- Axes: X={graph_analysis['axes']['x']}, Y={graph_analysis['axes']['y']}\n"
                        if graph_analysis.get('economic_interpretation'):
                            feedback_text += f"- Economic Interpretation: {graph_analysis['economic_interpretation']}\n"

                    feedback_sections.append(feedback_text)

                except Exception as e:
                    logger.error(f"Error while grading question {q_id}: {str(e)}")
                    try:
                        # グラフ分析が正常に完了している場合は続行を試みる
                        if requires_graph and graph_analysis and graph_analysis.get('success'):
                            marks, feedback = await self.evaluate_answer(q_id, answer_text, graph_analysis)
                            total_marks += marks

                            available_marks = sum(c['marks'] for c in self.criteria.get(q_id, []))
                            total_available += available_marks

                            marks_and_feedback[q_id] = {
                                "marks": marks,
                                "feedback": feedback,
                                "available_marks": available_marks
                            }

                            feedback_sections.append(feedback_text)
                            continue
                    except Exception as inner_e:
                        logger.error(f"Error in recovery attempt for question {q_id}: {str(inner_e)}")

                    # エラー回復に失敗した場合は0点を加算
                    total_marks += 0
                    feedback_sections.append(
                        f"\n## Question {q_id}\n"
                        f"Total Marks: {sum(c['marks'] for c in self.criteria.get(q_id, []))} points\n"
                        f"Score: 0 points\n\n"
                        f"Feedback: Error occurred during grading - {str(e)}\n"
                    )

            # Step 4: マークダウンコンバーターで結果を整形
            feedback_document = self.markdown_converter.create_feedback_document(
                ocr_result, marks_and_feedback, candidate_number
            )

            # Step 5: 結果を保存
            output_file = self.output_dir / f"{candidate_number}.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(feedback_document)
            self.all_feedback_contents.append(feedback_document) # 個別フィードバックをリストに追加

            # OCR詳細レポートを保存
            ocr_report = self.markdown_converter.create_ocr_report(ocr_result)
            ocr_report_file = self.output_dir / f"{candidate_number}_ocr_details.md"
            with open(ocr_report_file, 'w', encoding='utf-8') as f:
                f.write(ocr_report)

            # 統計情報を更新
            self.statistics['total_exams'] += 1
            self.statistics['total_pages'] += ocr_result.get("total_pages", 0)
            self.statistics['processing_time'] += time.time() - start_time

            # 合計点と採点者情報を追加
            feedback_sections.extend([
                f"## Total Score: {total_marks}/{total_available} points",
                f"### Marker: {self.marker_name}",
                f"\nMarking Date: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"\nProcessing Time: {time.time() - start_time:.1f} seconds"
            ])

            logger.info(f"Processing completed for {candidate_number} (Total Score: {total_marks} points)")
            return "\n\n".join(feedback_sections)

        except Exception as e:
            logger.error(f"Error occurred while processing PDF: {str(e)}")
            return f"Error processing PDF {pdf_path}: {str(e)}"

    async def _wait_for_rate_limit(self):
        """Control API call rate."""
        now = time.time()
        if self.last_api_call > 0:
            elapsed = now - self.last_api_call
            if elapsed < self.MIN_API_INTERVAL:
                await asyncio.sleep(self.MIN_API_INTERVAL - elapsed)
        self.last_api_call = time.time()

    def _find_question_page(self, ocr_result: Dict[str, Any], q_id: str) -> Optional[int]:
        """
        質問のページ番号を検索する

        Args:
            ocr_result: OCR処理結果
            q_id: 質問ID (例: "1a")

        Returns:
            ページ番号 (1から始まる) または None
        """
        page_texts = ocr_result.get("page_texts", [])

        for page_info in page_texts:
            page_num = page_info.get("page_num")
            text = page_info.get("text", "")

            # 質問IDのパターンを検索
            patterns = [
                rf"question\s*{q_id}",  # Question 1a
                rf"q\.?\s*{q_id}",      # Q1a or Q.1a
                rf"\b{q_id}\)",         # 1a)
                rf"\b{q_id}\.",         # 1a.
                rf"problem\s*{q_id}",    # Problem 1a
                rf"^{q_id}$"            # 1a
            ]

            for pattern in patterns:
                if re.search(pattern, text.lower()):
                    return page_num

        # ページが見つからない場合はNoneを返す
        return None

    async def _generate_content(self, contents, image_path: Optional[str] = None, quota_retry: bool = False):
        """
        Generate content using Gemini API asynchronously, optionally including an image.

        Args:
            contents: Text prompt for the model
            image_path: Optional path to an image file to include
            quota_retry: Flag to indicate if this is a retry after a quota error

        Returns:
            Generated content from Gemini API
        """
        loop = asyncio.get_event_loop()

        try:
            if image_path and os.path.exists(image_path):
                # 画像ファイルを読み込む（検証のみ）
                await loop.run_in_executor(
                    self.executor,
                    partial(Image.open, image_path)
                )

                # 画像を読み込んでバイナリデータに変換
                image_data = await loop.run_in_executor(
                    self.executor,
                    partial(
                        Image.open,
                        image_path
                    )
                )

                # マルチモーダル入力を生成（ストリーミングなし）
                response = await loop.run_in_executor(
                    self.executor,
                    partial(
                        self.model.generate_content,
                        [image_data, contents],
                        generation_config=self.generation_config
                    )
                )
                return response
            else:
                # テキストのみのコンテンツを生成
                logger.info("Generating Gemini API content (text-only)")
                logger.debug("Request content:")
                logger.debug(contents)

                response = await loop.run_in_executor(
                    self.executor,
                    partial(
                        self.model.generate_content,
                        [contents],
                        generation_config=self.generation_config
                    )
                )

                logger.info("Gemini API response received")
                logger.debug("Response content:")
                logger.debug(response.text if hasattr(response, 'text') else str(response))

                return response

        except Exception as e:
            error_str = str(e)
            logger.error(f"Error generating content: {error_str}")

            # 429 You exceeded your current quota エラーの特別処理
            if "429 You exceeded your current quota" in error_str:
                if not quota_retry:
                    # 一度目のリトライ
                    logger.warning("Quota exceeded error detected. Waiting 30 seconds before retrying...")
                    await asyncio.sleep(30)  # 30秒待機
                    logger.info("Retrying API call after quota error...")
                    return await self._generate_content(contents, image_path, quota_retry=True)
                else:
                    # 二度目のリトライ（エラーメッセージを返す）
                    logger.error("Quota exceeded error persists after retry. Returning error message.")
                    # エラーメッセージを含むダミーレスポンスを作成
                    from types import SimpleNamespace
                    dummy_response = SimpleNamespace()
                    dummy_response.text = "Error: API quota exceeded. Please try again later."
                    return dummy_response

            # その他のレートリミットエラー
            elif "rate limit" in error_str.lower():
                # 通常のレートリミットの場合は少し待機して再試行
                logger.warning("Rate limit error detected. Waiting 5 seconds before retrying...")
                await asyncio.sleep(5)
                return await self._generate_content(contents, image_path, quota_retry)

            # その他のエラー
            logger.error(f"Unhandled error: {error_str}")
            raise

    async def handle_mistral_api_error(self, func, *args, **kwargs):
        """
        Handle Mistral API errors and implement retry logic

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function execution result
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                if hasattr(result, 'ocr_data'):
                    return result.ocr_data
                return result
            except Exception as e:
                logger.error(f"Mistral API error: {str(e)} (attempt {attempt + 1}/{max_retries})")
                if "rate limit" in str(e).lower():
                    await asyncio.sleep(30)  # レートリミットの場合は30秒待機
                else:
                    await asyncio.sleep(2 ** attempt)  # 指数バックオフ
                if attempt == max_retries - 1:
                    logger.error("Max retries reached, returning empty OCR data")
                    return {
                        "success": False,
                        "error": str(e),
                        "ocr_data": {
                            "graph_type": "Economic Graph",
                            "axes": {"x": "X-axis", "y": "Y-axis"},
                            "trends": [],
                            "points_of_interest": [],
                            "economic_interpretation": "Basic economic relationships are illustrated.",
                            "raw_response": ""
                        }
                    }

    def get_statistics(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        return {
            **self.statistics,
            'average_time_per_exam': (
                self.statistics['processing_time'] / self.statistics['total_exams']
                if self.statistics['total_exams'] > 0 else 0
            ),
            'api_calls_per_exam': (
                self.statistics['api_calls'] / self.statistics['total_exams']
                if self.statistics['total_exams'] > 0 else 0
            ),
        }

    def _find_answer_from_ocr_results(self, ocr_result: Dict[str, Any], q_id: str) -> str:
        """
        Find student answer for a specific question from OCR results.

        Args:
            ocr_result: OCR processing results
            q_id: Question ID (e.g., "1a", "2b")

        Returns:
            Extracted answer text
        """
        logger.info(f"Looking for answer to question {q_id}")

        # 構造化データから検索
        for qa in ocr_result.get("structured_data", {}).get("questions_and_answers", []):
            if qa.get("question_id") == q_id:
                answer = qa.get("student_answer", "")
                if answer:
                    logger.info(f"Found answer for question {q_id} in structured data")
                    return answer

        # 見つからない場合は全文から抽出を試みる
        full_text = ocr_result.get("extracted_text", "")
        answer = self._extract_answer_from_text(full_text, q_id)

        if answer:
            logger.info(f"Found answer for question {q_id} using pattern matching")
            return answer

        # パターンマッチングでも見つからない場合は、問題文に基づく検索を試みる
        if q_id in self.questions:
            logger.info(f"Attempting content-based search for question {q_id}")
            question_text = self.questions[q_id]

            # 問題文から特徴的なキーワードを抽出
            import re
            # 不要な記号を削除
            cleaned_text = re.sub(r'[^\w\s]', ' ', question_text.lower())
            # 単語に分割
            words = cleaned_text.split()
            # ストップワードを除去（簡易版）
            stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as']
            keywords = [w for w in words if w not in stop_words and len(w) > 3]

            if keywords:
                # 各ページのテキストを検索
                for page_info in ocr_result.get("page_texts", []):
                    page_text = page_info.get("text", "").lower()

                    # キーワードの出現回数をカウント
                    keyword_count = sum(1 for kw in keywords if kw in page_text)

                    # 十分なキーワードが見つかった場合、その周辺のテキストを抽出
                    if keyword_count >= min(2, len(keywords)):
                        # ページテキストを段落に分割
                        paragraphs = page_text.split('\n\n')

                        # 最もキーワードを含む段落を特定
                        best_para = ""
                        max_keywords = 0

                        for para in paragraphs:
                            if len(para.strip()) < 10:  # 短すぎる段落はスキップ
                                continue

                            para_keyword_count = sum(1 for kw in keywords if kw in para.lower())
                            if para_keyword_count > max_keywords:
                                max_keywords = para_keyword_count
                                best_para = para

                        if best_para:
                            # 問題文を含む段落の次の段落を回答として抽出
                            para_index = paragraphs.index(best_para)
                            if para_index + 1 < len(paragraphs):
                                answer_para = paragraphs[para_index + 1]
                                if len(answer_para.strip()) > 10:  # 十分な長さがあれば回答とみなす
                                    logger.info(f"Found potential answer for question {q_id} using content-based search")
                                    return answer_para.strip()

        # 最終手段：問題番号の代わりに単独のアルファベットで検索
        if len(q_id) >= 2 and q_id[0].isdigit() and q_id[-1].isalpha():
            base_q_num = q_id[:-1]  # e.g., "1" from "1a"
            sub_q_char = q_id[-1]   # e.g., "a" from "1a"

            # 単独のアルファベットで質問を検索（小文字と大文字の両方）
            for qa in ocr_result.get("structured_data", {}).get("questions_and_answers", []):
                qa_id = qa.get("question_id", "")
                # 小文字または大文字のアルファベットが一致するか確認
                if qa_id and (qa_id[-1] == sub_q_char or qa_id[-1] == sub_q_char.upper()):
                    answer = qa.get("student_answer", "")
                    if answer:
                        logger.info(f"Found answer for question {q_id} by matching sub-question letter '{sub_q_char}' or '{sub_q_char.upper()}'")
                        return answer

            # 単独のアルファベットのみの質問IDを探す（例：'a'や'A'のみ）
            for qa in ocr_result.get("structured_data", {}).get("questions_and_answers", []):
                qa_id = qa.get("question_id", "")
                # 単独のアルファベットの場合
                if qa_id and len(qa_id) == 1 and qa_id.lower() == sub_q_char:
                    answer = qa.get("student_answer", "")
                    if answer:
                        logger.info(f"Found answer for question {q_id} by matching standalone letter '{qa_id}'")
                        return answer

            # 問題番号のみの質問IDを探し、その後の回答を探す（例：'2'の後に'b'や'c'が続く場合）
            base_q_found = False
            next_letter_expected = False
            expected_letter = ""

            # まず問題番号のみの質問を探す
            for qa in ocr_result.get("structured_data", {}).get("questions_and_answers", []):
                qa_id = qa.get("question_id", "")

                # 問題番号のみの場合（例：'2'）
                if qa_id and qa_id.isdigit() and qa_id == base_q_num:
                    base_q_found = True
                    logger.info(f"Found base question number {base_q_num}")
                    # 次の質問が小問の可能性がある
                    next_letter_expected = True
                    expected_letter = "a"  # 最初の小問は通常 'a'
                    continue

                # 問題番号の後に小問が続く場合
                if base_q_found and next_letter_expected:
                    # 単独のアルファベットの場合（例：'b'）
                    if qa_id and len(qa_id) == 1 and qa_id.isalpha():
                        # 期待される小問と一致するか確認
                        if qa_id.lower() == sub_q_char:
                            answer = qa.get("student_answer", "")
                            if answer:
                                logger.info(f"Found answer for question {q_id} by matching sequence: base number '{base_q_num}' followed by letter '{qa_id}'")
                                return answer
                        # 次の小問を期待
                        expected_letter = chr(ord(expected_letter) + 1)
                    else:
                        # 小問のシーケンスが終了
                        next_letter_expected = False

        logger.warning(f"No answer found for question {q_id}")
        return ""

    def save_all_feedback(self, output_filename: str = "all_exam_feedback.md") -> None:
        """
        Combines all processed feedback documents and saves them to a single markdown file.

        Args:
            output_filename: The name of the file to save all feedback to.
        """
        if not self.all_feedback_contents:
            logger.info("No feedback content to save.")
            return

        combined_feedback = "\n\n---\n\n".join(self.all_feedback_contents)

        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)

        output_path = self.output_dir / output_filename
        try:
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(combined_feedback)
            logger.info(f"All feedback saved to {output_path}")
        except IOError as e:
            logger.error(f"Failed to save all feedback to {output_path}: {e}")

