#!/usr/bin/env python3
"""
Mistral Exam Marker quick install script
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """Install required packages and configure"""
    print("Starting Mistral Exam Marker installation...")
    
    # Install required packages
    print("Installing required packages...")
    packages = [
        "typer",
        "rich",
        "python-dotenv",
        "aiohttp",
        "google-generativeai",
        "PyMuPDF",
        "Pillow",
        "pydantic"
    ]
    
    subprocess.check_call([sys.executable, "-m", "pip", "install"] + packages)
    
    # プロジェクトをインストール
    print("Installing project...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", "."])
    
    # run.pyに実行権限を付与
    run_py = Path(__file__).parent / "run.py"
    if run_py.exists():
        print("Setting execute permissions for run.py...")
        run_py.chmod(run_py.stat().st_mode | 0o111)
    
    print("\nInstallation complete!")
    print("You can run it with:")
    print("  ./run.py")
    print("Or:")
    print("  python -m mistral_exam_marker")
    
    # .envファイルの確認
    env_file = Path(__file__).parent / ".env"
    env_example = Path(__file__).parent / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("\n.env file not found. Copy from .env.example? [y/N]")
        response = input().lower()
        if response == "y" or response == "yes":
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print(".env file created. Please set your API keys.")
        else:
            print("Did not create .env file. Please create it manually before starting.")

if __name__ == "__main__":
    main()
