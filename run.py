#!/usr/bin/env python3
"""
Mistral Exam Marker startup script
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# カレントディレクトリの .env ファイルを優先的に読み込む
current_dir = Path.cwd()
current_dotenv_path = current_dir / ".env"
script_dir = Path(__file__).parent.absolute()
script_dotenv_path = script_dir / ".env"

# 読み込み優先順位: 1. カレントディレクトリ 2. スクリプトディレクトリ 3. デフォルト
if current_dotenv_path.exists():
    print(f"Attempting to load .env file from current directory: {current_dotenv_path}")
    load_dotenv(dotenv_path=current_dotenv_path)
    print(".env file from current directory loaded successfully.")
elif script_dotenv_path.exists():
    print(f"Attempting to load .env file from script directory: {script_dotenv_path}")
    load_dotenv(dotenv_path=script_dotenv_path)
    print(".env file from script directory loaded successfully.")
else:
    print("No .env file found in current or script directory. Trying default load_dotenv().")
    if load_dotenv():
        print("Default load_dotenv() found and loaded a .env file.")
    else:
        print("No .env file found by default load_dotenv() either.")

# 環境変数が読み込まれたか確認
marker_name_from_env = os.getenv("MARKER_NAME")
print(f"MARKER_NAME from .env: {marker_name_from_env}")
gemini_api_key = os.getenv("GEMINI_API_KEY")
print(f"GEMINI_API_KEY loaded: {'Yes' if gemini_api_key else 'No'}")

# スクリプトのディレクトリを取得 (Pathオブジェクトのscript_dirは上で定義済み)

# プロジェクトのソースディレクトリをPYTHONPATHに追加
sys.path.insert(0, str(script_dir))
sys.path.insert(0, str(script_dir / "src"))

try:
    # モジュールの存在確認
    import mistral_exam_marker
except ImportError:
    print("Module not found. Running setup...")
    import subprocess
    subprocess.call([sys.executable, "-m", "pip", "install", "-e", "."])
    print("Module installed. Trying to run again...")
    import mistral_exam_marker

try:
    # アプリケーションを実行
    from mistral_exam_marker.__main__ import main
    main()
except ImportError as e:
    print(f"Error: Failed to import module: {e}")
    print("\nPlease run setup:")
    print("  ./setup.sh")
    print("Or:")
    print("  pip install -e .")
    sys.exit(1)
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
