# Exam Marker (v1.0.1)

Mistral OCRとGemini APIを統合した経済学試験採点システム

## 概要

**Exam Marker**は、AI技術を駆使して経済学の試験採点を革新する次世代の自動評価システムです。Mistral AIの最先端OCR技術とGoogle Geminiの強力な分析能力を融合させることで、従来は時間と労力を要した試験採点プロセスを劇的に効率化します。手書きの答案用紙から複雑な経済グラフまで、様々な形式の回答を高精度に認識・分析し、一貫性のある公平な評価と詳細なフィードバックを提供します。教育者の貴重な時間を解放し、学生には迅速で質の高いフィードバックを届けることで、教育体験全体の質を向上させます。

- 📝 PDFからのテキストと画像の高精度抽出
- 📊 経済学的なグラフや図表の自動認識と分析
- 🔄 構造化されたデータへの変換
- 🧠 Gemini APIによる採点と詳細なフィードバック生成
- 📋 マークダウン形式でのレポート出力
- 🖥️ 簡単に使えるGUIインターフェース

## システムアーキテクチャ

以下のシーケンス図は、Exam Markerの主要コンポーネントとデータの流れを示しています:

```mermaid
sequenceDiagram
    participant ユーザー
    participant CLI/GUI
    participant MistralExamMarker
    participant DocumentProcessor
    participant OCRClient
    participant MistralAPI
    participant GeminiAPI
    participant MarkdownConverter
    
    ユーザー->>CLI/GUI: PDFファイル/フォルダを選択
    CLI/GUI->>MistralExamMarker: process_pdf(pdf_path)で処理開始
    
    MistralExamMarker->>DocumentProcessor: process_pdf(pdf_path)
    DocumentProcessor->>OCRClient: 初期化
    
    loop 各ページの処理
        DocumentProcessor->>DocumentProcessor: テキスト抽出
        DocumentProcessor->>DocumentProcessor: 画像の抽出・一時保存
        
        loop 各画像の処理
            DocumentProcessor->>OCRClient: analyze_image(image_path)
            OCRClient->>MistralAPI: OCRと画像解析
            MistralAPI-->>OCRClient: OCR結果を返却
            OCRClient-->>DocumentProcessor: OCR結果と画像分析
            
            DocumentProcessor->>DocumentProcessor: グラフ認識・軸情報抽出
            DocumentProcessor->>DocumentProcessor: グラフメタデータ保存
        end
        
        DocumentProcessor->>DocumentProcessor: 質問と回答の特定(_extract_qa_pairs)
    end
    
    DocumentProcessor-->>MistralExamMarker: OCR結果・グラフ・テキスト
    
    loop 各質問の採点
        MistralExamMarker->>MistralExamMarker: 回答テキスト抽出(_find_answer_from_ocr_results)
        MistralExamMarker->>MistralExamMarker: 関連グラフ特定(_find_relevant_graph)
        
        Note over MistralExamMarker: 質問とグラフ<br/>のマッチング
        
        MistralExamMarker->>MistralExamMarker: レート制限(_wait_for_rate_limit)
        
        loop 各採点基準の評価
            alt グラフ基準かつイメージパスが存在
                MistralExamMarker->>GeminiAPI: 画像とテキストの両方を送信
            else テキストのみ
                MistralExamMarker->>GeminiAPI: テキストのみで評価リクエスト
            end
            
            GeminiAPI-->>MistralExamMarker: JSON形式の採点結果
            
            Note over MistralExamMarker: グラフの採点が<br/>必要な場合の<br/>自動検出と<br/>0点処理
            
            MistralExamMarker->>MistralExamMarker: 採点結果を集計
        end
    end
    
    MistralExamMarker->>MarkdownConverter: create_feedback_document
    MarkdownConverter-->>MistralExamMarker: マークダウン形式のフィードバック
    
    MistralExamMarker->>MarkdownConverter: create_ocr_report
    MarkdownConverter-->>MistralExamMarker: OCR詳細レポート
    
    MistralExamMarker->>MistralExamMarker: 結果の保存・統計更新
    MistralExamMarker-->>CLI/GUI: 処理結果
    CLI/GUI-->>ユーザー: 完了通知・統計表示
```

### 処理フロー

1. **ドキュメントの入力**:ユーザーがPDFまたは画像形式の試験答案を提供
2. **前処理**:DocumentProcessorがファイルを解析し、テキストと画像を抽出
3. **OCR処理**:OCR Clientが抽出された画像をMistral APIに送信し分析
4. **構造化**:認識されたテキスト、グラフ、表、数式が構造化データに変換
5. **グラフ分析**:抽出されたグラフ画像が質問と関連付け、特徴が抽出される
6. **マルチモーダル評価**:テキスト回答とグラフ画像の両方がGemini APIに送信
7. **採点**:各採点基準が個別に評価され、特にグラフが必要な基準は画像も評価
8. **レポート作成**:結果がMarkdown形式に変換され、最終レポートとして出力

## 主な機能

1. **高度なドキュメント認識**
   - PDFからのテキスト抽出
   - 数式やグラフの認識
   - 表の構造解析
   - 手書き文字の認識

2. **経済学的分析**
   - 需要供給グラフの自動認識
   - 経済モデルの図表解析
   - 数式と計算の検証
   - 経済用語の文脈理解

3. **採点システム**
   - 詳細な採点基準に基づく評価
   - 一貫性のある採点
   - 建設的なフィードバック生成
   - 統計分析とレポート作成

4. **使いやすいインターフェース**
   - シンプルなGUIでフォルダ選択
   - 処理状況のリアルタイム表示
   - 終了時のポップアップ通知
   - 詳細なログ表示

## インストール

リポジトリをクローンし、依存関係をインストールします:

```bash
git clone https://github.com/YOUR_USERNAME/exam-marker.git
cd exam-marker
pip install -r requirements.txt
```

※ `requirements.txt` には `asyncio` を含めていません。Python 標準ライブラリのため、別途インストール不要です。

## 環境設定

1. `.env.example`を`.env`にコピーして必要な情報を設定:
   ```bash
   cp .env.example .env
   ```

2. 以下の環境変数を設定:
   - `MISTRAL_API_KEY`: Mistral AI APIキー
   - `GEMINI_API_KEY`: Google Gemini APIキー
   - 設問、主要な解答、採点基準、採点ガイドラインなどを設定

## アプリケーションの実行

以下のコマンドでヘルプ表示およびGUI/CLIモード実行が可能です:

```bash
# ヘルプ表示
python -m mistral_exam_marker --help

# GUIモード
python -m mistral_exam_marker --gui

# CLIモード (PDFファイル指定、出力先オプション)
python -m mistral_exam_marker exam1.pdf exam2.pdf -o /path/to/output
```


# （不要なスクリプト参照を削除）


### 統計情報の出力形式

処理完了時に表示される統計情報は、実行モードによって以下のように異なります：

**CLIモード**:
- 処理したファイル数
- 合計ページ数
- 総処理時間
- ファイルあたりの平均処理時間
- API呼び出し回数

**GUIモード**:
- 処理したファイル数
- 総処理時間
- 出力フォルダのパス

実行すると、フォルダ選択ダイアログが表示され、採点するPDFファイルが入ったフォルダを選択できます。処理が完了すると、結果は選択したフォルダに保存され、処理完了の通知が表示されます。

## 使用方法

### 1. GUIモード(推奨)

# インストール

python quick_install.py
pip install mistralai
pip install google-generativeai

プログラムを引数なしで実行するだけです:

```bash

# インストール済みの場合
mistral-exam-marker

# または直接実行する場合
python -m mistral_exam_marker

# または

chmod +x run.py && ./run.py

# または仮想環境がアクティベートされている状態で実行する場合（例）
source venv/bin/activate && mistral-exam-marker --gui

# またはPYTHONPATH環境変数を設定してsrcディレクトリを追加し、仮想環境を再アクティベートする状態で実行する場合（例）
export PYTHONPATH=$PYTHONPATH:/path/to/exam-marker/src && source venv/bin/activate && mistral-exam-marker --gui

```

1. フォルダ選択ダイアログが表示されます
2. 採点するPDFファイルが入ったフォルダを選択します
3. 処理が開始され、進捗状況がターミナルに表示されます
4. 処理が完了すると、結果のサマリーとポップアップ通知が表示されます
5. 採点結果は選択したフォルダに保存されます

### 2. コマンドライン

```bash
# 単一のファイルを処理
mistral-exam-marker path/to/exam.pdf

# 複数のファイルを処理
mistral-exam-marker path/to/exam1.pdf path/to/exam2.pdf

# 出力先を指定
mistral-exam-marker path/to/exam.pdf --output /custom/output/dir

# GUIモードで実行
mistral-exam-marker --gui
```

### 3. プログラムから使用

```python
from mistral_exam_marker import MistralExamMarker

# マーカーの初期化
marker = MistralExamMarker(
    mistral_api_key="your_mistral_key",
    gemini_api_key="your_gemini_key",
    marker_name="採点者名"
)

# PDFの処理と採点
feedback = await marker.process_pdf("path/to/exam.pdf")
print(feedback)
```

2. **採点基準の設定**
   - `.env`ファイルで質問、模範解答、採点基準を定義
   - 各質問に対する配点と評価基準を細かく設定可能

3. **バッチ処理**
   ```python
   import asyncio
   from pathlib import Path

   async def process_batch(pdf_dir: str):
       pdfs = list(Path(pdf_dir).glob("*.pdf"))
       for pdf in pdfs:
           feedback = await marker.process_pdf(str(pdf))
           print(f"Processed: {pdf.name}")

   asyncio.run(process_batch("path/to/pdf/directory"))
   ```

## 出力例 (例)

本システムが生成する Markdown フィードバックは以下のようになります:

```markdown
Candidate Number: 12345

## Grading Details
Question 1a (18/20 points)
需要と供給の関係について、グラフを用いた説明が非常に明確です。

## Chart Analysis
- Page 1 Chart
  - Type: 需要供給グラフ
  - Axes: X=数量, Y=価格
  - Economic Interpretation: 市場均衡の変化を正確に図示

...

## Total Score: 85/100
Marker: Tomoto Masuda
```

## 注意事項

1. **APIキーの管理**
   - APIキーは必ず`.env`ファイルで管理
   - コマンドパレット（⌘+Shift+P）→「Developer: Reload Window」を実行すると、キャッシュされた環境変数がクリアされます。これで、開いているプロジェクトごとに正しい .env が読み込まれるようになります。

2. **システムの制限**
   - 一度に処理できるPDFの最大ページ数は10ページ
   - 画像の解像度は最低300dpi推奨
   - 手書き文字の認識精度は書き方に依存

3. **エラー処理**
   - APIエラーは自動的にリトライ
   - 処理結果は常にログに記録
   - エラー発生時は詳細な情報を出力

## カスタマイズ

システムは以下の部分をカスタマイズ可能です:

1. **採点基準**
   - `.env`ファイルで質問ごとの採点基準を定義
   - 配点と評価項目の調整が可能

2. **OCR設定**
   - 認識精度のしきい値
   - 対応言語の設定
   - 画像処理パラメータ

3. **出力形式**
   - フィードバックのフォーマット
   - レポートのテンプレート
   - 統計情報の表示形式
  
## 関連論文

ChatGPT: Is It Reliable as an Automated Writing Evaluation Tool?

https://doi.org/10.18039/ajesi.1463503

## ライセンス

MIT License - 詳細は[LICENSE](LICENSE)ファイルを参照してください。
