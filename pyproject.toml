[project]
name = "mistral-exam-marker"
version = "0.1.0"
description = "Mistral OCRとGemini APIを活用した経済学試験の採点システム"
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
dependencies = [
    "aiohttp>=3.8.0",
    "google-generativeai>=0.3.0",
    "PyMuPDF>=1.20.0",
    "Pillow>=9.0.0",
    "python-dotenv>=0.19.0",
    "pydantic>=2.0.0",
    "typer>=0.9.0",
    "rich>=13.0.0",
    "asyncio>=3.4.3",
    "attrs>=23.0.0",  # aiohttp の依存関係
    # tkinter is part of the standard library, so no need to add it as a dependency
]
requires-python = ">=3.9"
readme = "README.md"
license = { text = "MIT" }

[project.urls]
repository = "https://github.com/yourusername/mistral-exam-marker"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src"

[project.scripts]
mistral-exam-marker = "mistral_exam_marker.__main__:main"